import { getUuid } from "@/lib/hash";
import path from "path";
import sharp from "sharp";

/**
 * Processes a static image file buffer to generate preview and thumbnail versions.
 * @param fileBuffer The buffer of the original image file.
 * @param filename The original filename.
 * @returns An object containing processed buffers, R2 keys, and image metadata.
 */
export async function processStaticImage(fileBuffer: Buffer, filename: string) {
  const image = sharp(fileBuffer);

  // Get metadata
  const metadata = await image.metadata();
  const { width, height, size, format } = metadata;
  if (!width || !height || !size || !format) {
    throw new Error("Could not read image metadata");
  }

  // Generate preview (max width 1920, webp)
  const previewBuffer = await image
    .resize(1920, null, { withoutEnlargement: true })
    .webp({ quality: 85 })
    .toBuffer();

  // Generate thumbnail (max width 400, webp)
  const thumbnailBuffer = await image
    .resize(400, null, { withoutEnlargement: true })
    .webp({ quality: 80 })
    .toBuffer();

  // Define R2 Keys
  const fileGroupId = getUuid();
  const baseFilename = path.parse(filename).name;
  const fileExtension = path.extname(filename).slice(1);

  const originalKey = `static/originals/${fileGroupId}/${baseFilename}.${fileExtension}`;
  const previewKey = `static/previews/${fileGroupId}/${baseFilename}.webp`;
  const thumbnailKey = `static/thumbnails/${fileGroupId}/${baseFilename}.webp`;
  
  const r2Keys = {
    original: originalKey,
    preview: previewKey,
    thumbnail: thumbnailKey,
  };

  const buffers = {
    original: fileBuffer,
    preview: previewBuffer,
    thumbnail: thumbnailBuffer,
  };
  
  const dbData = {
    static_image_original_key: originalKey,
    static_image_preview_key: previewKey,
    static_image_thumbnail_key: thumbnailKey,
    width,
    height,
    file_size_bytes: size,
    format: format,
  };

  return { r2Keys, buffers, dbData };
} 