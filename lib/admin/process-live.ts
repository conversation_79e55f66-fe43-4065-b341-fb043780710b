import { getUuid } from "@/lib/hash";
import path from "path";
import sharp from "sharp";
import ffmpeg from "fluent-ffmpeg";
import { path as ffmpegPath } from "@ffmpeg-installer/ffmpeg";
import { path as ffprobePath } from "@ffprobe-installer/ffprobe";
import { Readable } from "stream";
import { promises as fs } from "fs";
import os from "os";

// Set ffmpeg and ffprobe paths
ffmpeg.setFfmpegPath(ffmpegPath);
ffmpeg.setFfprobePath(ffprobePath);

/**
 * A robust method to get a poster frame by writing to a temporary file.
 */
const getPosterBufferRobust = (fileBuffer: Buffer): Promise<Buffer> => {
  const tempInputPath = path.join(os.tmpdir(), `ffmpeg-in-${Date.now()}.mp4`);
  const tempOutputPath = path.join(os.tmpdir(), `ffmpeg-out-${Date.now()}.jpg`);

  return new Promise(async (resolve, reject) => {
    try {
      await fs.writeFile(tempInputPath, fileBuffer);

      ffmpeg(tempInputPath)
        .seekInput("00:00:01.000")
        .frames(1)
        .format("mjpeg")
        .output(tempOutputPath)
        .on("end", async () => {
          try {
            const buffer = await fs.readFile(tempOutputPath);
            resolve(buffer);
          } catch (readError) {
            reject(readError);
          }
        })
        .on("error", (err) => {
          reject(new Error(`ffmpeg poster generation failed: ${err.message}`));
        })
        .run();
    } catch (writeError) {
      reject(writeError);
    } finally {
      // Cleanup temporary files
      // Use a timeout to ensure ffmpeg has released the files
      setTimeout(() => {
        fs.unlink(tempInputPath).catch(() => {});
        fs.unlink(tempOutputPath).catch(() => {});
      }, 1000);
    }
  });
};

/**
 * Extracts metadata, generates poster images, and a video thumbnail from a live wallpaper.
 * @param fileBuffer The buffer of the original video file.
 * @param filename The original filename.
 * @returns An object containing processed buffers, R2 keys, and video metadata.
 */
export async function processLiveWallpaper(fileBuffer: Buffer, filename:string) {
  // 1. Get Video Metadata
  const getVideoMetadata = () => new Promise<ffmpeg.FfprobeData>((resolve, reject) => {
    const probeStream = new Readable();
    probeStream.push(fileBuffer);
    probeStream.push(null);
    ffmpeg(probeStream).ffprobe((err, data) => {
      if (err) reject(new Error(`ffprobe error: ${err.message}`));
      resolve(data);
    });
  });

  const metadata = await getVideoMetadata();
  const videoMeta = metadata.streams.find(s => s.codec_type === 'video');
  if (!videoMeta || !videoMeta.width || !videoMeta.height || !videoMeta.duration) {
    throw new Error('Could not extract video metadata.');
  }

  // 2. Extract Poster Frame and generate webp versions (using the new robust method)
  const posterBuffer = await getPosterBufferRobust(fileBuffer);
  
  const posterImage = sharp(posterBuffer);
  const posterPreviewBuffer = await posterImage.resize(1920, null, { withoutEnlargement: true }).webp({ quality: 85 }).toBuffer();
  const posterThumbnailBuffer = await posterImage.resize(400, null, { withoutEnlargement: true }).webp({ quality: 80 }).toBuffer();

  // 3. Generate Video Thumbnail
  const getVideoThumbnailBuffer = () =>
    new Promise<Buffer>((resolve, reject) => {
      const videoThumbStream = new Readable();
      videoThumbStream.push(fileBuffer);
      videoThumbStream.push(null);
      const chunks: any[] = [];
      ffmpeg(videoThumbStream)
        .setDuration(5)
        .noAudio()
        .size("400x?") // Simple scaling
        .outputOptions([
          "-movflags faststart", // Essential for web playback
        ])
        .toFormat("mp4")
        .pipe()
        .on("data", (chunk) => chunks.push(chunk))
        .on("end", () => resolve(Buffer.concat(chunks)))
        .on("error", (err) =>
          reject(new Error(`ffmpeg video thumb error: ${err.message}`))
        );
    });

  const videoThumbnailBuffer = await getVideoThumbnailBuffer();
  
  // 4. Define R2 Keys
  const fileGroupId = getUuid();
  const baseFilename = path.parse(filename).name;

  const originalKey = `live/videos/${fileGroupId}/${baseFilename}.mp4`;
  const posterPreviewKey = `live/posters/${fileGroupId}/${baseFilename}.webp`;
  const posterThumbnailKey = `live/thumbnails/${fileGroupId}/${baseFilename}-poster.webp`;
  const videoThumbnailKey = `live/thumbnails/${fileGroupId}/${baseFilename}-video.mp4`;

  return {
    buffers: {
      original: fileBuffer,
      posterPreview: posterPreviewBuffer,
      posterThumbnail: posterThumbnailBuffer,
      videoThumbnail: videoThumbnailBuffer,
    },
    r2Keys: {
      original: originalKey,
      posterPreview: posterPreviewKey,
      posterThumbnail: posterThumbnailKey,
      videoThumbnail: videoThumbnailKey,
    },
    dbData: {
      live_video_key: originalKey,
      live_poster_image_key: posterPreviewKey,
      static_image_thumbnail_key: posterThumbnailKey, // For frontend simplicity (initial static display)
      live_thumbnail_video_key: videoThumbnailKey,   // For frontend hover effect
      width: videoMeta.width,
      height: videoMeta.height,
      duration_seconds: Math.round(Number(videoMeta.duration)),
      file_size_bytes: fileBuffer.length,
      format: 'mp4',
    },
  };
} 