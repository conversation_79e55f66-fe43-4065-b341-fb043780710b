"use client";

import * as React from "react";
import Autoplay from "embla-carousel-autoplay";

import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { WallpaperCard } from "@/components/blocks/wallpaper/WallpaperCard";

// NOTE: This interface definition MUST be kept in sync with the one in WallpaperCard.tsx
// To avoid duplication in the future, consider moving this to a shared types file.
interface Wallpaper {
  id: string;
  title: string;
  wallpaper_type: 'static' | 'live';
  device_target: 'mobile' | 'desktop' | 'any';
  static_image_original_url: string | null;
  static_image_thumbnail_url: string | null;
  live_poster_image_url?: string | null;
  live_thumbnail_video_url?: string | null;
  live_video_url?: string | null;
  tags?: string[] | null;
}

interface HomepageHeroProps {
  wallpapers: Wallpaper[];
}

export function HomepageHero({ wallpapers }: HomepageHeroProps) {
  const plugin = React.useRef(
    Autoplay({ delay: 4000, stopOnInteraction: true })
  );

  if (!wallpapers || wallpapers.length === 0) {
    return (
      <div className="relative h-[400px] w-full flex items-center justify-center border-2 border-dashed rounded-lg">
        <p className="text-muted-foreground">精选壁纸正在路上...</p>
      </div>
    );
  }

  return (
    <Carousel
      plugins={[plugin.current]}
      className="w-full"
      opts={{
        align: "start",
        loop: true,
      }}
      onMouseEnter={plugin.current.stop}
      onMouseLeave={plugin.current.reset}
    >
      <CarouselContent className="-ml-4">
        {wallpapers.map((wallpaper) => (
          <CarouselItem 
            key={wallpaper.id} 
            className="pl-4 basis-1/2 md:basis-1/3 lg:basis-1/4"
          >
            <div className="h-full">
              <WallpaperCard wallpaper={wallpaper} />
            </div>
          </CarouselItem>
        ))}
      </CarouselContent>
      <CarouselPrevious className="ml-14 hidden md:flex" />
      <CarouselNext className="mr-14 hidden md:flex" />
    </Carousel>
  );
} 