"use client";

import { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { Badge } from "@/components/ui/badge";
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { useTranslations } from 'next-intl';

// This interface should be kept in sync with WallpaperGrid.tsx
interface Wallpaper {
  id: string;
  title: string;
  wallpaper_type: 'static' | 'live';
  device_target: 'mobile' | 'desktop' | 'any';
  static_image_original_url: string | null;
  static_image_thumbnail_url: string | null;
  live_poster_image_url?: string | null;
  live_thumbnail_video_url?: string | null;
  live_video_url?: string | null;
  tags?: string[] | null;
}

interface WallpaperCardProps {
  wallpaper: Wallpaper;
}

export function WallpaperCard({ wallpaper }: WallpaperCardProps) {
  const [isHovering, setIsHovering] = useState(false);
  const t = useTranslations('WallpaperCard');
  
  const staticThumbnailUrl = wallpaper.static_image_thumbnail_url;
  const previewVideoUrl = wallpaper.live_video_url;

  const handleDownload = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    // 1. Fire-and-forget request to the counting API.
    // We don't need to wait for it.
    fetch(`/api/wallpapers/${wallpaper.id}/download`);

    // 2. Immediately start the download on the client side.
    const downloadUrl = wallpaper.wallpaper_type === 'static'
      ? wallpaper.static_image_original_url
      : wallpaper.live_video_url;

    if (!downloadUrl) {
      console.error("Download failed: No URL available for this wallpaper.");
      // Optionally, show a toast notification to the user
      // toast.error("Sorry, this download is currently unavailable.");
      return;
    }

    const a = document.createElement('a');
    a.href = downloadUrl;
    a.download = ''; // This encourages the browser to download rather than navigate
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
  };

  return (
    <TooltipProvider delayDuration={100}>
      <div 
        className="group block relative overflow-hidden rounded-xl transition-all duration-300 ease-in-out hover:shadow-xl hover:-translate-y-1"
        onMouseEnter={() => setIsHovering(true)}
        onMouseLeave={() => setIsHovering(false)}
      >
          {/* Image, Video, and Link Container */}
          <Link href={`/wallpaper/${wallpaper.id}`} className={cn(
            "block w-full h-full relative bg-secondary",
            wallpaper.device_target === 'desktop' ? 'aspect-video' : 'aspect-[9/16]'
          )}>
              {/* Layer 0: Static Thumbnail Image */}
              {staticThumbnailUrl ? (
                  <Image
                      src={staticThumbnailUrl}
                      alt={wallpaper.title}
                      fill
                      className="object-cover transition-transform duration-300 group-hover:scale-105"
                      sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"
                  />
              ) : (
                  <div className="w-full h-full flex items-center justify-center">
                      <span className="text-muted-foreground text-sm">No Image</span>
                  </div>
              )}

              {/* Layer 10: Video Preview Layer */}
              {wallpaper.wallpaper_type === 'live' && previewVideoUrl && (
                  <video
                      src={previewVideoUrl}
                      autoPlay
                      loop
                      muted
                      playsInline
                      className={cn(
                          "absolute inset-0 w-full h-full object-cover transition-opacity duration-300 z-10",
                          isHovering ? "opacity-100" : "opacity-0"
                      )}
                  />
              )}
          </Link>
          
          {/* Layer 20: UI Controls */}
          {/* Gradient for text legibility */}
          <div className="absolute inset-x-0 bottom-0 h-24 bg-gradient-to-t from-black/60 to-transparent z-20 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />

          {/* Title and Download button container */}
          <div className="absolute inset-x-0 bottom-0 z-30 p-4 flex justify-between items-start opacity-0 group-hover:opacity-100 transition-opacity duration-300">
              <h3 className="text-sm font-semibold text-white drop-shadow-md pr-2">
                  {wallpaper.title}
              </h3>
              <Tooltip>
                  <TooltipTrigger asChild>
                      <Button
                          size="icon"
                          variant="secondary"
                          className="rounded-full h-9 w-9 flex-shrink-0 bg-black/20 backdrop-blur-sm hover:bg-black/40"
                          onClick={handleDownload}
                      >
                          <Download className="h-4 w-4 text-white" />
                      </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                      <p>{t('download')}</p>
                  </TooltipContent>
              </Tooltip>
          </div>

          {/* Live Badge */}
          {wallpaper.wallpaper_type === 'live' && (
              <Badge variant="destructive" className="absolute top-3 right-3 z-30">
                  LIVE
              </Badge>
          )}
      </div>
    </TooltipProvider>
  );
} 