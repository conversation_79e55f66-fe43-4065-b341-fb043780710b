"use client";

import { WallpaperCard } from "./WallpaperCard";
import Empty from "@/components/blocks/empty";

// Define a type for the wallpaper object based on the API response.
// This should be moved to a central types file (e.g., types/wallpaper.d.ts) later.
interface Wallpaper {
  id: string;
  title: string;
  wallpaper_type: 'static' | 'live';
  device_target: 'mobile' | 'desktop' | 'any';
  static_image_original_url: string | null;
  static_image_thumbnail_url: string | null;
  live_poster_image_url?: string | null;
  live_thumbnail_video_url?: string | null;
  live_video_url?: string | null;
  tags?: string[] | null;
  // Add other properties as needed from your API response
  // For example: live_poster_image_url, live_video_url etc.
}

interface WallpaperGridProps {
  wallpapers: Wallpaper[];
}

export function WallpaperGrid({ wallpapers }: WallpaperGridProps) {
  if (!wallpapers || wallpapers.length === 0) {
    return (
      <div className="py-20">
        <Empty message="No Wallpapers Found. Try adjusting your filters." />
      </div>
    );
  }

  return (
    <div className="columns-2 sm:columns-3 md:columns-4 lg:columns-5 gap-4 space-y-4">
      {wallpapers.map((wallpaper) => (
        <div key={wallpaper.id} className="break-inside-avoid">
          <WallpaperCard wallpaper={wallpaper} />
        </div>
      ))}
    </div>
  );
} 