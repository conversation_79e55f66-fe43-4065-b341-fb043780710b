"use client";

import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';

// Reusable Pill component for the filter bar
const FilterPill = ({
  label,
  value,
  currentValue,
  onClick,
}: {
  label: string;
  value: string;
  currentValue: string;
  onClick: () => void;
}) => (
  <button
    onClick={onClick}
    className={cn(
      "px-4 py-2 rounded-full text-sm font-semibold transition-colors duration-200 ease-in-out",
      currentValue === value
        ? "bg-primary text-primary-foreground shadow-sm"
        : "bg-secondary text-secondary-foreground hover:bg-muted"
    )}
  >
    {label}
  </button>
);

export function FilterBar() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const t = useTranslations('FilterBar');

  const handleFilterChange = (key: string, value: string | null) => {
    const current = new URLSearchParams(Array.from(searchParams.entries()));

    if (!value || value === "all") {
      current.delete(key);
    } else {
      current.set(key, value);
    }
    
    // Reset page to 1 when any filter changes
    current.set("page", "1");

    const search = current.toString();
    const query = search ? `?${search}` : "";

    router.push(`${pathname}${query}`, { scroll: false });
  };
  
  const type = searchParams.get('type') || 'all';
  const device = searchParams.get('device') || 'all';

  const filterGroups = [
    {
      key: 'type',
      currentValue: type,
      options: [
        { label: t('allTypes'), value: 'all' },
        { label: t('static'), value: 'static' },
        { label: t('live'), value: 'live' },
      ],
    },
    {
      key: 'device',
      currentValue: device,
      options: [
        { label: t('allDevices'), value: 'all' },
        { label: t('desktop'), value: 'desktop' },
        { label: t('mobile'), value: 'mobile' },
      ],
    },
  ];

  return (
    <div className="flex flex-col sm:flex-row items-center justify-center gap-4 sm:gap-8">
      {filterGroups.map((group) => (
        <div key={group.key} className="flex items-center gap-2 bg-secondary p-1 rounded-full">
          {group.options.map((option) => (
            <FilterPill
              key={option.value}
              label={option.label}
              value={option.value}
              currentValue={group.currentValue}
              onClick={() => handleFilterChange(group.key, option.value)}
            />
          ))}
        </div>
      ))}
    </div>
  );
} 