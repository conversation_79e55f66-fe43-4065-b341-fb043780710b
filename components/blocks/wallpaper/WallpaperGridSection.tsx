import { Link } from "@/i18n/routing";
import { WallpaperCard } from "./WallpaperCard";
import { useTranslations } from "next-intl";
import { LucideIcon } from "lucide-react";

// 使用与 WallpaperCard 相同的 Wallpaper 接口定义
interface Wallpaper {
  id: string;
  title: string;
  wallpaper_type: 'static' | 'live';
  device_target: 'mobile' | 'desktop' | 'any';
  static_image_original_url: string | null;
  static_image_thumbnail_url: string | null;
  live_poster_image_url?: string | null;
  live_thumbnail_video_url?: string | null;
  live_video_url?: string | null;
  tags?: string[] | null;
}

interface WallpaperGridSectionProps {
  title: string;
  wallpapers: Wallpaper[];
  viewAllHref: string;
  icon?: LucideIcon;
}

export function WallpaperGridSection({ 
  title, 
  wallpapers, 
  viewAllHref,
  icon: IconComponent
}: WallpaperGridSectionProps) {
  const t = useTranslations('LandingPage');
  if (!wallpapers || wallpapers.length === 0) {
    return null;
  }

  return (
    <section className="w-full mb-16">
      <div className="container mx-auto px-4">
        {/* 标题和查看全部按钮 */}
        <div className="flex items-center justify-between mb-8">
          <h2 className="text-2xl md:text-3xl font-bold text-primary flex items-center gap-3">
            {IconComponent && <IconComponent className="size-8 text-primary" />}
            {title}
          </h2>
          <Link 
            href={viewAllHref}
            className="text-primary hover:text-primary/80 transition-colors font-medium"
          >
            {t('view_all')} →
          </Link>
        </div>

        {/* 壁纸网格 */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 md:gap-6">
          {wallpapers.map((wallpaper) => (
            <WallpaperCard 
              key={wallpaper.id} 
              wallpaper={wallpaper} 
            />
          ))}
        </div>
      </div>
    </section>
  );
} 