"use client";

import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>ationContent,
  PaginationE<PERSON>psis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";
import { usePathname, useSearchParams } from 'next/navigation';
import { cn } from "@/lib/utils";

interface Props {
  pagination: {
    current_page: number;
    total_pages: number;
  };
  'aria-label'?: string;
}

// Custom PaginationLink to apply Apple-like styles
const CustomPaginationLink = ({ isActive, ...props }: React.ComponentProps<typeof PaginationLink>) => (
    <PaginationLink
        isActive={isActive}
        className={cn(
            "rounded-full",
            isActive ? "bg-primary text-primary-foreground" : "hover:bg-muted"
        )}
        {...props}
    />
);

export function WallpaperPagination({ pagination, 'aria-label': ariaLabel = "pagination" }: Props) {
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const { current_page, total_pages } = pagination;

  const createPageURL = (pageNumber: number | string) => {
    const params = new URLSearchParams(searchParams);
    params.set('page', pageNumber.toString());
    return `${pathname}?${params.toString()}`;
  };

  const renderPageNumbers = () => {
    const pageNumbers = [];
    // Show pages around the current page
    const startPage = Math.max(1, current_page - 2);
    const endPage = Math.min(total_pages, current_page + 2);

    if (startPage > 1) {
      pageNumbers.push(
        <PaginationItem key="1">
          <PaginationLink href={createPageURL(1)}>1</PaginationLink>
        </PaginationItem>
      );
      if (startPage > 2) {
        pageNumbers.push(<PaginationEllipsis key="start-ellipsis" />);
      }
    }

    for (let i = startPage; i <= endPage; i++) {
      pageNumbers.push(
        <PaginationItem key={i}>
          <CustomPaginationLink href={createPageURL(i)} isActive={i === current_page}>
            {i}
          </CustomPaginationLink>
        </PaginationItem>
      );
    }

    if (endPage < total_pages) {
      if (endPage < total_pages - 1) {
        pageNumbers.push(<PaginationEllipsis key="end-ellipsis" />);
      }
      pageNumbers.push(
        <PaginationItem key={total_pages}>
          <PaginationLink href={createPageURL(total_pages)}>{total_pages}</PaginationLink>
        </PaginationItem>
      );
    }
    
    return pageNumbers;
  };

  if (total_pages <= 1) {
    return null; // Don't render pagination if there's only one page
  }

  return (
    <Pagination aria-label={ariaLabel}>
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious 
            href={createPageURL(current_page - 1)}
            aria-disabled={current_page <= 1}
            className={cn("rounded-full hover:bg-muted", current_page <= 1 ? "pointer-events-none opacity-50" : "")}
          />
        </PaginationItem>

        {renderPageNumbers()}

        <PaginationItem>
          <PaginationNext 
            href={createPageURL(current_page + 1)}
            aria-disabled={current_page >= total_pages}
            className={cn("rounded-full hover:bg-muted", current_page >= total_pages ? "pointer-events-none opacity-50" : "")}
          />
        </PaginationItem>
      </PaginationContent>
    </Pagination>
  );
} 