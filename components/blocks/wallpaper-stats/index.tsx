"use client";

import { useEffect, useState } from "react";
import { MotionDiv } from "@/components/motion";
import { AnimatedCounter } from "./animated-counter";
import { useTranslations } from "next-intl";

function StatCard({ value, title, description }: { value: number, title: string, description: string }) {
  return (
    <MotionDiv
      initial={{ opacity: 0, y: 10 }}
      whileInView={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, ease: "easeInOut" }}
      viewport={{ once: true }}
      className="flex flex-col items-center text-center"
    >
      <div className="text-5xl md:text-6xl font-bold text-primary">
        <AnimatedCounter value={value} />
      </div>
      <h3 className="mt-2 text-lg font-semibold text-foreground">{title}</h3>
      <p className="text-sm text-muted-foreground">{description}</p>
    </MotionDiv>
  );
}

export function WallpaperStats() {
    const [stats, setStats] = useState({ total: 1099, live: 688, downloads: 12831 }); // Added downloads for display

    const t = useTranslations('LandingPage');
    // useEffect(() => {
    //     async function fetchStats() {
    //         try {
    //             const response = await fetch('/api/wallpapers/stats');
    //             if (!response.ok) {
    //                 console.error('Failed to fetch stats');
    //                 return;
    //             }
    //             const data = await response.json();
    //             if (data.data) {
    //                 // Merging fetched stats with the initial downloads count
    //                 setStats(prevStats => ({ ...prevStats, ...data.data }));
    //             }
    //         } catch (error) {
    //             console.error("Error fetching wallpaper stats:", error);
    //         }
    //     }

    //     fetchStats();
    // }, []);

    return (
        <section className="py-12 sm:py-20">
            <div className="container mx-auto px-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8 md:gap-12 max-w-5xl mx-auto">
                <StatCard 
                  value={stats.total} 
                  title={t('labubu_universe')} 
                  description={`${t('over')} ${stats.total} ${t('handcrafted_wallpapers')}`} 
                />
                <StatCard 
                  value={stats.live} 
                  title={t('brought_to_life')} 
                  description={`${t('with')} ${stats.live} ${t('animated_scenes')}`} 
                />
                <StatCard 
                  value={stats.downloads} 
                  title={t('loved_by_fans')} 
                  description={`${t('more_than')} ${Math.floor(stats.downloads / 100) * 100}+ ${t('downloads_worldwide')}`} 
                />
              </div>
            </div>
        </section>
    )
} 