"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Download, Smartphone, Monitor, Scan, FileBox } from "lucide-react";

// This type should be consistent with other definitions
interface Wallpaper {
    id: string;
    title: string;
    description: string | null;
    wallpaper_type: 'static' | 'live';
    device_target: 'mobile' | 'desktop' | 'any';
    width: number;
    height: number;
    file_size_bytes: number;
    static_image_original_url: string | null;
    live_video_url?: string | null; // Corrected field name
}

interface WallpaperInfoProps {
    wallpaper: Wallpaper;
}

// Helper to format file size
const formatBytes = (bytes: number, decimals = 2) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
};

export function WallpaperInfo({ wallpaper }: WallpaperInfoProps) {
    const downloadUrl = wallpaper.wallpaper_type === 'live' 
        ? wallpaper.live_video_url 
        : wallpaper.static_image_original_url;

    const downloadButtonText = wallpaper.wallpaper_type === 'live'
        ? "Download Live Wallpaper"
        : "Download HD Wallpaper";

    const handleDownload = (e: React.MouseEvent) => {
        e.preventDefault();

        // 1. Fire-and-forget request to the counting API.
        fetch(`/api/wallpapers/${wallpaper.id}/download`);

        // 2. Immediately start the download on the client side.
        if (!downloadUrl) {
            console.error("Download failed: No URL available for this wallpaper.");
            return;
        }

        const a = document.createElement('a');
        a.href = downloadUrl;
        a.download = ''; // This encourages the browser to download rather than navigate
        a.style.display = 'none';
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
    };

    const details = [
        { icon: Scan, label: 'Resolution', value: `${wallpaper.width} x ${wallpaper.height}` },
        { icon: FileBox, label: 'File Size', value: formatBytes(wallpaper.file_size_bytes) },
        { icon: wallpaper.device_target === 'mobile' ? Smartphone : Monitor, label: 'Device', value: wallpaper.device_target, hide: wallpaper.device_target === 'any' },
    ];

    return (
        <div className="flex flex-col gap-y-6">
            <Badge variant="secondary" className="capitalize w-fit">
                {wallpaper.wallpaper_type} Wallpaper
            </Badge>

            <h1 className="text-4xl lg:text-5xl font-extrabold tracking-tighter bg-clip-text text-transparent bg-gradient-to-br from-gray-900 to-gray-600 dark:from-gray-50 dark:to-gray-400">
                {wallpaper.title}
            </h1>
            
            {wallpaper.description && (
                <p className="text-lg text-muted-foreground">{wallpaper.description}</p>
            )}

            <ul className="space-y-4 text-foreground/80 mt-2">
                {details.map((detail) => !detail.hide && (
                    <li key={detail.label} className="flex items-center gap-x-3">
                        <detail.icon className="h-5 w-5 text-primary" aria-hidden="true" />
                        <span><span className="font-semibold text-foreground">{detail.label}:</span> {detail.value}</span>
                    </li>
                ))}
            </ul>
            
            {downloadUrl && (
                <Button size="lg" className="mt-6 w-full sm:w-auto text-base font-bold tracking-wide" onClick={handleDownload}>
                    <Download className="mr-2 h-5 w-5" />
                    {downloadButtonText}
                </Button>
            )}
        </div>
    );
} 