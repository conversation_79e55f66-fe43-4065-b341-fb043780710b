"use client";

interface VideoPlayerProps {
    src: string;
    poster?: string;
}

export function VideoPlayer({ src, poster }: VideoPlayerProps) {
    return (
        <div className="w-full h-full flex items-center justify-center">
            <video
                src={src}
                poster={poster}
                controls
                autoPlay
                loop
                muted
                playsInline
                className="w-auto h-auto max-w-full max-h-full rounded-lg"
            >
                Your browser does not support the video tag.
            </video>
        </div>
    );
} 