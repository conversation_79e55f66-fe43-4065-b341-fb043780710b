import Image from 'next/image';
import { VideoPlayer } from './VideoPlayer';

// A more complete type definition for a single wallpaper.
// Ideally, this would live in a shared types file.
interface Wallpaper {
    id: string;
    title: string;
    wallpaper_type: 'static' | 'live';
    static_image_preview_url: string | null;
    live_video_url?: string | null; // Assuming this field exists for video source
    live_poster_image_url?: string | null; // Poster for the video
}

interface WallpaperPreviewProps {
    wallpaper: Wallpaper;
}

export function WallpaperPreview({ wallpaper }: WallpaperPreviewProps) {
    return (
        <div className="w-full max-h-[85vh] aspect-[9/16] flex items-center justify-center rounded-2xl overflow-hidden">
            {/* The background and border are now gone, only the content remains */}
            
            {wallpaper.wallpaper_type === 'static' && wallpaper.static_image_preview_url && (
                <Image
                    src={wallpaper.static_image_preview_url}
                    alt={wallpaper.title}
                    width={1080}
                    height={1920}
                    className="w-auto h-auto max-w-full max-h-full object-contain"
                    priority
                />
            )}
            {wallpaper.wallpaper_type === 'live' && wallpaper.live_video_url && (
                <div className="w-full h-full flex items-center justify-center">
                    <VideoPlayer 
                        src={wallpaper.live_video_url}
                        poster={wallpaper.live_poster_image_url || undefined}
                    />
                </div>
            )}
        </div>
    );
} 