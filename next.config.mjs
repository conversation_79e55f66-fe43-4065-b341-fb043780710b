import bundleAnalyzer from "@next/bundle-analyzer";
import createNextIntlPlugin from "next-intl/plugin";
import mdx from "@next/mdx";
import { createRequire } from "module";
const require = createRequire(import.meta.url);

const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === "true",
});

const withNextIntl = createNextIntlPlugin();

const withMDX = mdx({
  options: {
    remarkPlugins: [],
    rehypePlugins: [],
  },
});

/** @type {import('next').NextConfig} */
const nextConfig = {
  output: "standalone",
  reactStrictMode: false,
  pageExtensions: ["ts", "tsx", "js", "jsx", "md", "mdx"],
  images: {
    remotePatterns: [
      {
        protocol: "https",
        hostname: "r2-images.labubuwallpics.com",
      },
    ],
  },
  serverExternalPackages: [
    "fluent-ffmpeg",
    "@ffmpeg-installer/ffmpeg",
    "@ffprobe-installer/ffprobe",
  ],
  webpack: (config, { isServer }) => {
    if (isServer) {
      // 1. Mark problematic packages as external
      const originalExternals = config.externals || [];
      config.externals = [
        ...(Array.isArray(originalExternals) ? originalExternals : [originalExternals]),
        {
          "fluent-ffmpeg": "commonjs fluent-ffmpeg",
          "@ffmpeg-installer/ffmpeg": "commonjs @ffmpeg-installer/ffmpeg",
          "@ffprobe-installer/ffprobe": "commonjs @ffprobe-installer/ffprobe",
        },
      ];

      // 2. Ignore binary files and problematic files
      config.module = config.module || {};
      config.module.rules = config.module.rules || [];
      config.module.rules.push(
        {
          test: /\.(md|txt)$/,
          loader: 'null-loader'
        },
        {
          test: /ffmpeg|ffprobe$/,
          loader: 'null-loader'
        }
      );

      // 3. Add null-loader for ignoring files
      config.resolveLoader = config.resolveLoader || {};
      config.resolveLoader.alias = config.resolveLoader.alias || {};
      config.resolveLoader.alias['null-loader'] = require.resolve('null-loader');
    }

    return config;
  },
  async redirects() {
    return [];
  },
};

// Make sure experimental mdx flag is enabled
const configWithMDX = {
  ...nextConfig,
  experimental: {
    mdxRs: true,
  },
};

export default withBundleAnalyzer(withNextIntl(withMDX(configWithMDX)));

import { initOpenNextCloudflareForDev } from "@opennextjs/cloudflare";
initOpenNextCloudflareForDev();
