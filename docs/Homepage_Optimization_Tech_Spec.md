# 首页布局优化 - 技术实现方案

**版本:** 1.0
**日期:** 2025-06-25

## 1. 概述

本文档旨在为"首页布局优化"功能提供具体的技术实现指导。目标是将现有单一列表的首页，重构为由"焦点轮播"、"热门下载"和"最新上架"三个独立板块组成的策展式页面。

## 2. 前端实现

### 2.1 页面与组件结构

我们将通过创建和组合多个React组件来实现新布局。

- **主页面文件**: `app/[locale]/(default)/page.tsx`
    - **职责**: 作为容器页面，负责获取所有板块所需的数据，并将数据分别传递给对应的子组件进行渲染。

- **新组件**:
    1.  **`components/blocks/hero/HomepageHero.tsx`**:
        - **用途**: 实现首页顶部的焦点轮播区。
        - **技术栈**: 使用 `shadcn/ui` 的 `Carousel` 组件为基础进行封装。
        - **Props**: 接收一个壁纸对象数组 `(wallpapers: Wallpaper[])`。

    2.  **`components/blocks/wallpaper/WallpaperGridSection.tsx`**:
        - **用途**: 一个可复用的板块组件，用于展示带有标题和"查看全部"链接的壁纸网格。将被"热门下载"和"最新上架"两个板块同时使用。
        - **Props**: 
            - `title: string` (板块标题，如"时下热门")
            - `wallpapers: Wallpaper[]` (要展示的壁纸数组)
            - `viewAllHref: string` ("查看全部"按钮的链接地址)

- **现有组件**:
    - **`components/blocks/wallpaper/WallpaperCard.tsx`**: 将被 `WallpaperGridSection` 复用，用于渲染网格中的单个壁纸卡片。可能需要为其增加新的交互样式（如 on-hover 效果）。

### 2.2 数据获取 (Data Fetching)

在 `app/[locale]/(default)/page.tsx` 中，我们将采用 Server Component 的方式，通过并行的网络请求获取两个列表的数据：

```typescript
// in app/[locale]/(default)/page.tsx
import { getWallpapers } from '@/models/wallpaper'; // 假设已有获取壁纸的数据模型函数

export default async function HomePage() {
  // 并行获取热门和最新壁纸
  const [popularWallpapers, newWallpapers] = await Promise.all([
    getWallpapers({ sort: 'downloads', order: 'desc', limit: 8 }),
    getWallpapers({ sort: 'created_at', order: 'desc', limit: 8 })
  ]);

  // 焦点轮播区的数据可以暂时从最新壁纸中选取前3个
  const heroWallpapers = newWallpapers.slice(0, 3);

  return (
    <main>
      <HomepageHero wallpapers={heroWallpapers} />
      <WallpaperGridSection 
        title="时下热门" 
        wallpapers={popularWallpapers} 
        viewAllHref="/posts?sort=popular" 
      />
      <WallpaperGridSection 
        title="最新发布" 
        wallpapers={newWallpapers} 
        viewAllHref="/posts?sort=newest" 
      />
    </main>
  );
}
```

### 2.3 路由与页面跳转

- "热门下载"板块的"查看全部"按钮将链接至 `/wallpapers?sort=popular`。
- "最新上架"板块的"查看全部"按钮将链接至 `/wallpapers?sort=newest`。
- 新建 `app/[locale]/(default)/wallpapers/page.tsx` 页面，以接收并处理 `sort` 这个 `searchParam`。它将根据 `sort` 的值（`popular` 或 `newest`）来决定其初始加载时的数据排序方式。

```typescript
// in app/[locale]/(default)/wallpapers/page.tsx
export default function AllWallpapersPage({ searchParams }: { searchParams: { sort?: string } }) {
  const initialSort = searchParams.sort === 'popular' ? 'popular' : 'newest';
  
  // ... 页面组件内部逻辑，将 initialSort 传递给获取数据的hook或组件
}
```

### 2.4 样式与交互

- **布局**: 使用 Tailwind CSS 的 Flexbox 和 Grid 系统来构建页面分区和网格布局。
- **响应式**: 通过 `md:`、`lg:` 等 Tailwind 前缀实现响应式断点。
- **动效**:
    - 鼠标悬停在 `WallpaperCard` 上时，使用 `group-hover` 和 `transition` 来实现平滑的放大/阴影效果。
    - 页面板块加载可使用 `framer-motion` 实现简单的淡入动画，提升质感。

## 3. 后端变更

**无需后端变更**。

现有的 `/api/wallpapers` 接口已支持 `sort`, `order`, 和 `limit` 等查询参数，完全可以满足前端的数据请求需求。

## 4. 实施步骤

1.  **创建组件**: 在 `components/` 目录下创建 `HomepageHero.tsx` 和 `WallpaperGridSection.tsx` 两个新文件。
2.  **实现组件逻辑**: 分别完成上述两个组件的UI和逻辑开发。
3.  **重构首页**: 修改 `app/[locale]/(default)/page.tsx`，移除旧的列表逻辑，引入新组件并实现数据获取和传递。
4.  **修改列表页**: 更新 `app/[locale]/(default)/wallpapers/page.tsx` 以支持通过 URL search param 控制排序。
5.  **样式微调**: 为 `WallpaperCard` 添加 `hover` 效果，并对整体页面进行样式和间距的微调。
6.  **测试**: 在不同尺寸的设备上进行全面的响应式测试和交互测试。 