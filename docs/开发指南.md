# Labubu 壁纸网站 - 开发指南

## 1. 开发环境搭建

### 1.1 系统要求
- **Node.js**: 18.0.0 或更高版本
- **pnpm**: 8.0.0 或更高版本 (推荐)
- **Git**: 最新版本

### 1.2 快速开始

#### 1. 克隆项目
```bash
git clone https://github.com/your-username/labubuwallpics.git
cd labubuwallpics
```

#### 2. 安装依赖
```bash
pnpm install
```

#### 3. 配置环境变量
```bash
cp .env.example .env.local
# 编辑 .env.local 填入必要的配置
```

#### 4. 启动开发服务器
```bash
pnpm dev
```

访问 `http://localhost:3000` 查看应用。

### 1.3 开发工具推荐

#### VS Code 扩展
- **ES7+ React/Redux/React-Native snippets**
- **Tailwind CSS IntelliSense**
- **TypeScript Importer**
- **Prettier - Code formatter**
- **ESLint**

#### 浏览器扩展
- **React Developer Tools**
- **Redux DevTools** (如果使用 Redux)

## 2. 项目结构详解

### 2.1 目录结构
```
labubuwallpics/
├── app/                    # Next.js App Router
│   ├── [locale]/          # 国际化路由
│   ├── api/               # API 路由
│   ├── globals.css        # 全局样式
│   └── layout.tsx         # 根布局
├── components/            # React 组件
│   ├── ui/               # 基础 UI 组件
│   ├── blocks/           # 业务组件
│   └── ...
├── lib/                  # 工具库
├── models/               # 数据模型
├── services/             # 业务服务
├── types/                # TypeScript 类型定义
├── i18n/                 # 国际化配置
├── public/               # 静态资源
├── docs/                 # 项目文档
└── ...
```

### 2.2 核心目录说明

#### app/ - 应用路由
- 使用 Next.js 15 App Router
- 支持嵌套布局和路由组
- API 路由与页面路由分离

#### components/ - 组件库
- `ui/`: Shadcn/UI 基础组件
- `blocks/`: 业务功能组件
- `console/`: 控制台相关组件

#### lib/ - 工具库
- `utils.ts`: 通用工具函数
- `resp.ts`: API 响应格式化
- `cache.ts`: 客户端缓存
- `storage.ts`: 文件存储

#### models/ - 数据模型
- 数据库操作封装
- 统一的数据访问接口
- 类型安全的查询方法

#### services/ - 业务服务
- 业务逻辑封装
- 跨模型的复杂操作
- 第三方服务集成

## 3. 开发规范

### 3.1 代码规范

#### TypeScript 规范
```typescript
// 使用接口定义类型
interface User {
  id: number;
  uuid: string;
  email: string;
  nickname?: string;
}

// 使用泛型提高复用性
interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

// 使用枚举定义常量
enum OrderStatus {
  Created = "created",
  Paid = "paid",
  Deleted = "deleted",
}
```

#### React 组件规范
```typescript
// 使用函数组件和 TypeScript
interface ButtonProps {
  children: React.ReactNode;
  variant?: 'primary' | 'secondary';
  onClick?: () => void;
}

export function Button({ children, variant = 'primary', onClick }: ButtonProps) {
  return (
    <button
      className={cn(
        'px-4 py-2 rounded',
        variant === 'primary' ? 'bg-blue-500 text-white' : 'bg-gray-200'
      )}
      onClick={onClick}
    >
      {children}
    </button>
  );
}
```

#### API 路由规范
```typescript
// app/api/example/route.ts
export async function POST(req: Request) {
  try {
    // 1. 参数验证
    const { param1, param2 } = await req.json();
    if (!param1) {
      return respErr("invalid params");
    }

    // 2. 权限检查
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    // 3. 业务逻辑
    const result = await businessLogic(param1, param2);

    // 4. 返回结果
    return respData(result);
  } catch (e) {
    console.log("api error:", e);
    return respErr("operation failed");
  }
}
```

### 3.2 命名规范

#### 文件命名
- **组件文件**: PascalCase (如 `UserProfile.tsx`)
- **工具文件**: kebab-case (如 `api-client.ts`)
- **页面文件**: lowercase (如 `page.tsx`, `layout.tsx`)

#### 变量命名
- **常量**: UPPER_SNAKE_CASE
- **变量**: camelCase
- **组件**: PascalCase
- **类型/接口**: PascalCase

### 3.3 Git 提交规范

#### 提交消息格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

#### 类型说明
- `feat`: 新功能
- `fix`: 修复 bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

#### 示例
```
feat(auth): add Google One Tap login

- Implement Google One Tap authentication
- Add credential validation
- Update login UI components

Closes #123
```

## 4. 开发工作流

### 4.1 功能开发流程

#### 1. 创建功能分支
```bash
git checkout -b feature/user-profile
```

#### 2. 开发功能
- 编写代码
- 添加测试
- 更新文档

#### 3. 提交代码
```bash
git add .
git commit -m "feat(profile): add user profile page"
```

#### 4. 推送并创建 PR
```bash
git push origin feature/user-profile
# 在 GitHub 上创建 Pull Request
```

### 4.2 代码审查

#### 审查要点
- [ ] 代码符合项目规范
- [ ] 功能实现正确
- [ ] 测试覆盖充分
- [ ] 文档更新完整
- [ ] 性能影响评估

### 4.3 测试策略

#### 单元测试
```typescript
// __tests__/utils.test.ts
import { formatDate } from '@/lib/utils';

describe('formatDate', () => {
  it('should format date correctly', () => {
    const date = new Date('2024-01-01');
    expect(formatDate(date)).toBe('2024-01-01');
  });
});
```

#### 集成测试
```typescript
// __tests__/api/user.test.ts
import { POST } from '@/app/api/get-user-info/route';

describe('/api/get-user-info', () => {
  it('should return user info', async () => {
    const request = new Request('http://localhost/api/get-user-info', {
      method: 'POST',
    });
    
    const response = await POST(request);
    const data = await response.json();
    
    expect(data.code).toBe(0);
  });
});
```

## 5. 常用开发任务

### 5.1 添加新页面

#### 1. 创建页面文件
```typescript
// app/[locale]/(default)/new-page/page.tsx
import { getTranslations } from 'next-intl/server';

export default async function NewPage({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  const t = await getTranslations();

  return (
    <div>
      <h1>{t('new_page.title')}</h1>
      {/* 页面内容 */}
    </div>
  );
}
```

#### 2. 添加国际化文本
```json
// i18n/messages/en.json
{
  "new_page": {
    "title": "New Page"
  }
}
```

#### 3. 更新导航 (如需要)
```json
// i18n/pages/landing/en.json
{
  "header": {
    "nav": {
      "items": [
        {
          "title": "New Page",
          "url": "/new-page"
        }
      ]
    }
  }
}
```

### 5.2 添加新 API 接口

#### 1. 创建 API 路由
```typescript
// app/api/new-endpoint/route.ts
import { respData, respErr } from "@/lib/resp";
import { getUserUuid } from "@/services/user";

export async function POST(req: Request) {
  try {
    const { param } = await req.json();
    
    if (!param) {
      return respErr("invalid params");
    }

    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    // 业务逻辑
    const result = await processData(param);

    return respData(result);
  } catch (e) {
    console.log("api error:", e);
    return respErr("operation failed");
  }
}
```

#### 2. 添加类型定义
```typescript
// types/api.d.ts
export interface NewEndpointRequest {
  param: string;
}

export interface NewEndpointResponse {
  result: string;
}
```

#### 3. 前端调用
```typescript
// 在组件中使用
const handleSubmit = async (data: NewEndpointRequest) => {
  const response = await fetch('/api/new-endpoint', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data),
  });
  
  const result = await response.json();
  if (result.code === 0) {
    // 处理成功
  }
};
```

### 5.3 添加新组件

#### 1. 创建组件文件
```typescript
// components/ui/new-component.tsx
import { cn } from "@/lib/utils";

interface NewComponentProps {
  className?: string;
  children: React.ReactNode;
}

export function NewComponent({ className, children }: NewComponentProps) {
  return (
    <div className={cn("base-styles", className)}>
      {children}
    </div>
  );
}
```

#### 2. 导出组件
```typescript
// components/ui/index.ts
export { NewComponent } from './new-component';
```

#### 3. 使用组件
```typescript
import { NewComponent } from '@/components/ui';

export function ExamplePage() {
  return (
    <NewComponent className="custom-styles">
      Content here
    </NewComponent>
  );
}
```

## 6. 调试技巧

### 6.1 前端调试

#### React DevTools
- 检查组件状态和 props
- 分析组件渲染性能
- 调试 Context 和 Hooks

#### 浏览器调试
```typescript
// 添加调试断点
debugger;

// 控制台输出
console.log('Debug info:', { data, error });

// 性能分析
console.time('operation');
// ... 操作代码
console.timeEnd('operation');
```

### 6.2 后端调试

#### API 调试
```typescript
// 详细日志输出
console.log('API Request:', {
  method: req.method,
  url: req.url,
  headers: req.headers,
  body: await req.json(),
});
```

#### 数据库调试
```typescript
// Supabase 查询调试
const { data, error } = await supabase
  .from('users')
  .select('*')
  .eq('email', email);

console.log('DB Query:', { data, error });
```

### 6.3 性能调试

#### 构建分析
```bash
# 分析包大小
npm run analyze

# 检查依赖
npm ls --depth=0
```

#### 运行时性能
```typescript
// 使用 React Profiler
import { Profiler } from 'react';

function onRenderCallback(id, phase, actualDuration) {
  console.log('Render:', { id, phase, actualDuration });
}

<Profiler id="App" onRender={onRenderCallback}>
  <App />
</Profiler>
```

## 7. 常见问题解决

### 7.1 构建问题

#### 依赖冲突
```bash
# 清理依赖
rm -rf node_modules pnpm-lock.yaml
pnpm install
```

#### TypeScript 错误
```bash
# 重新生成类型
pnpm run type-check
```

### 7.2 运行时问题

#### 环境变量未生效
```typescript
// 检查环境变量
console.log('ENV Check:', {
  NODE_ENV: process.env.NODE_ENV,
  SUPABASE_URL: process.env.SUPABASE_URL,
});
```

#### 数据库连接问题
```typescript
// 测试数据库连接
import { getSupabaseClient } from '@/models/db';

const testConnection = async () => {
  try {
    const supabase = getSupabaseClient();
    const { data, error } = await supabase.from('users').select('count');
    console.log('DB Connection:', { success: !error, error });
  } catch (e) {
    console.error('DB Connection Failed:', e);
  }
};
```

## 8. 最佳实践

### 8.1 性能优化

#### 组件优化
```typescript
// 使用 React.memo 避免不必要的重渲染
export const ExpensiveComponent = React.memo(({ data }) => {
  return <div>{/* 复杂渲染逻辑 */}</div>;
});

// 使用 useMemo 缓存计算结果
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data);
}, [data]);
```

#### 图片优化
```typescript
// 使用 Next.js Image 组件
import Image from 'next/image';

<Image
  src="/wallpaper.jpg"
  alt="Wallpaper"
  width={800}
  height={600}
  loading="lazy"
  placeholder="blur"
/>
```

### 8.2 安全实践

#### 输入验证
```typescript
// 使用 Zod 进行数据验证
import { z } from 'zod';

const userSchema = z.object({
  email: z.string().email(),
  name: z.string().min(1).max(100),
});

export async function POST(req: Request) {
  try {
    const body = await req.json();
    const validatedData = userSchema.parse(body);
    // 使用验证后的数据
  } catch (e) {
    return respErr("invalid input");
  }
}
```

#### 权限检查
```typescript
// 统一权限检查
const checkPermission = async (requiredRole: string) => {
  const user = await getCurrentUser();
  if (!user || user.role !== requiredRole) {
    throw new Error('Insufficient permissions');
  }
  return user;
};
```

### 8.3 代码组织

#### 自定义 Hooks
```typescript
// hooks/useUser.ts
export function useUser() {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchUser().then(setUser).finally(() => setLoading(false));
  }, []);

  return { user, loading };
}
```

#### Context 使用
```typescript
// contexts/user.tsx
const UserContext = createContext<UserContextType | undefined>(undefined);

export function useUserContext() {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUserContext must be used within UserProvider');
  }
  return context;
}
```

## 9. 开发工具配置

### 9.1 VS Code 配置

#### settings.json
```json
{
  "typescript.preferences.importModuleSpecifier": "relative",
  "editor.formatOnSave": true,
  "editor.codeActionsOnSave": {
    "source.fixAll.eslint": true
  },
  "tailwindCSS.experimental.classRegex": [
    ["cn\\(([^)]*)\\)", "'([^']*)'"]
  ]
}
```

### 9.2 ESLint 配置

#### .eslintrc.json
```json
{
  "extends": ["next/core-web-vitals"],
  "rules": {
    "@typescript-eslint/no-unused-vars": "error",
    "prefer-const": "error",
    "no-console": "warn"
  }
}
```

### 9.3 Prettier 配置

#### .prettierrc
```json
{
  "semi": true,
  "trailingComma": "es5",
  "singleQuote": true,
  "printWidth": 80,
  "tabWidth": 2
}
```

---

**开发检查清单**:
- [ ] 代码符合项目规范
- [ ] 添加必要的类型定义
- [ ] 实现错误处理
- [ ] 添加适当的测试
- [ ] 更新相关文档
- [ ] 检查性能影响
- [ ] 验证安全性
- [ ] 测试多种场景
