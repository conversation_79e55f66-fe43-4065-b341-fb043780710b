# Labubu 壁纸网站 - MVP 产品需求文档 (PRD)

## 1. 项目概述

### 1.1 项目名称
Labubu 壁纸站 (Labubu Walls - 暂定)

### 1.2 项目目标
打造一个简洁、美观、易用的 Labubu 主题壁纸网站，为 Labubu 爱好者提供高质量的静态和动态壁纸下载服务，支持桌面和移动设备。MVP 版本旨在快速上线核心功能，收集用户反馈，并验证产品价值。

### 1.3 目标用户
*   Labubu 潮玩爱好者
*   喜欢可爱、潮流、个性化壁纸的用户
*   希望为自己的桌面和手机设备寻找独特壁纸的用户

### 1.4 核心价值主张
*   **精选内容：** 提供高质量、多样化的 Labubu 主题壁纸。
*   **便捷体验：** 用户可以轻松浏览、筛选并下载适配其设备的静态及动态壁纸。
*   **专注主题：** 聚焦 Labubu 品牌，打造粉丝社群归属感。

## 2. 产品功能 (MVP)

### 2.1 全局功能
*   **响应式设计：** 网站在桌面、平板和移动设备上均有良好的视觉和交互体验。
*   **简洁导航：** 提供清晰的网站导航结构。
*   **版权与声明：** 页脚包含必要的版权信息和使用声明。

### 2.2 首页 / 壁纸列表页
*   **功能描述：** 展示所有可用的 Labubu 壁纸，并允许用户通过筛选器快速找到目标壁纸。
*   **页面组成：**
    *   **2.2.1 导航栏 (Header)**
        *   **Logo 与网站名称：** 固定在页面顶部左侧，点击可返回首页。
        *   **(可选 MVP) 搜索框：** 简单文本搜索框，允许用户按壁纸标题进行模糊搜索 (如果实现)。
    *   **2.2.2 筛选栏 (Filter Bar)**
        *   **位置：** 主内容区上方，导航栏下方。
        *   **筛选条件：**
            *   **壁纸类型 (Type)：**
                *   选项：全部 (All - 默认)、静态 (Static)、动态 (Live)
                *   交互：按钮组或标签页形式，用户点击切换。
            *   **设备适配 (Device)：**
                *   选项：全部 (All - 默认)、电脑 (Desktop)、手机 (Mobile)
                *   交互：按钮组或标签页形式，用户点击切换。
            *   **扩展性：** 设计上应考虑未来可方便地增加新的筛选维度（如排序、颜色等）。
    *   **2.2.3 壁纸列表区 (Wallpaper Grid)**
        *   **布局：** 网格布局 (Grid Layout)。
        *   **图片懒加载：** 滚动到可视区域时才加载图片。
        *   **壁纸卡片 (Wallpaper Card)：**
            *   **缩略图/预览：**
                *   **静态壁纸：** 显示静态缩略图。
                *   **动态壁纸：**
                    *   默认显示高质量的封面图/海报帧。
                    *   卡片右上角或左上角显示“LIVE”小标签或播放图标，以区分动态壁纸。
                    *   **桌面端悬停预览：** 鼠标悬停在动态壁纸卡片上时，卡片内的视频自动静音循环播放短预览。
                    *   **移动端交互：** 点击卡片直接进入详情页进行播放。
            *   **卡片信息区 (位于缩略图下方)：**
                *   **壁纸标题：** 清晰显示壁纸的名称。
                *   **设备类型指示：** 显示小图标（如电脑图标、手机图标）以表明该壁纸主要适配的设备类型。若壁纸同时适配多种设备或为通用型，则可不显示或显示通用图标。
            *   **交互：** 点击整个卡片（除特定交互元素外）均可跳转到对应的壁纸详情页。
    *   **2.2.4 分页组件 (Pagination)**
        *   **位置：** 壁纸列表区下方。
        *   **功能：** 提供“上一页”、“下一页”及页码跳转功能，方便用户浏览更多壁纸。

### 2.3 壁纸详情页
*   **功能描述：** 展示单张壁纸的详细信息和高清预览，并提供下载功能。
*   **页面组成：**
    *   **2.3.1 导航栏 (Header)：** 与首页保持一致。
    *   **2.3.2 壁纸预览区：**
        *   **静态壁纸：**
            *   展示清晰、大尺寸的壁纸预览图。
        *   **动态壁纸：**
            *   展示一个带有播放控件的视频播放器。
            *   控件包括：播放/暂停、音量控制 (带静音开关)、进度条、全屏切换。
            *   默认自动播放（可考虑静音自动播放）。
    *   **2.3.3 壁纸信息与操作区 (通常在预览区右侧或下方)：**
        *   **壁纸标题：** 醒目展示。
        *   **壁纸属性：**
            *   **类型：** 明确标示“静态壁纸”或“动态壁纸”。
            *   **适配设备：** 明确标示“电脑壁纸”、“手机壁纸”或“通用”。
            *   **(可选 MVP) 分辨率：** 例如 1920x1080。
            *   **(可选 MVP) 文件大小。**
            *   **(可选 MVP，动态壁纸) 时长。**
        *   **下载按钮：**
            *   设计醒目，文字清晰，如“下载高清壁纸”、“下载 MP4”。
            *   点击后直接触发对应壁纸文件的下载。
            *   **动态壁纸：** MVP 版本仅提供 MP4 格式下载。按钮文字可明确为“下载 MP4 动态壁纸”。未来可扩展支持其他动态壁纸格式。
        *   **(可选 MVP) 分享按钮：** 允许用户将壁纸链接分享到主流社交平台。
    *   **2.3.4 (可选 MVP) 相关推荐区：**
        *   在页面底部展示若干张相关的或同类型的其他 Labubu 壁纸，引导用户继续浏览。

### 2.4 内容管理 (MVP 后台简化方案)
*   MVP 阶段，不设专门的图形化后台管理界面。
*   壁纸内容的上传和元数据管理将通过以下方式进行：
    *   **图片/视频文件：** 管理员手动上传至 Cloudflare R2。
    *   **元数据：** 管理员通过直接操作数据库（如使用数据库客户端工具）或运行预设脚本来添加/修改壁纸的标题、类型、设备适配、R2 文件路径等信息。

## 3. 非功能性需求 (MVP)

*   **3.1 性能：**
    *   页面加载速度快，尤其是图片和视频预览的加载。
    *   列表滚动流畅。
*   **3.2 易用性：**
    *   界面直观，用户无需学习即可快速上手。
    *   筛选和下载流程简单明了。
*   **3.3 兼容性：**
    *   兼容主流现代浏览器 (Chrome, Firefox, Safari, Edge 最新版本)。
    *   在主流操作系统 (Windows, macOS, Android, iOS) 上表现一致。
*   **3.4 可访问性 (Accessibility - a11y)：**
    *   图片有描述性的 `alt` 文本。
    *   交互元素有清晰的标签和焦点状态。
    *   颜色对比度满足基本可读性要求。
*   **3.5 安全性：**
    *   防止常见的 Web 漏洞 (如 XSS，如果未来有用户输入内容)。
    *   保护 R2 存储桶的访问权限。

## 4. 未来展望 (MVP 之后)

*   用户账户系统 (注册、登录、个人收藏)。
*   用户上传壁纸功能及审核机制。
*   点赞、评论、评分系统。
*   更高级的搜索与筛选 (按颜色、标签、上传日期等)。
*   壁纸排行榜 (热门下载、最新上传)。
*   个性化推荐系统。
*   多语言支持。
*   完善的后台管理系统。
*   支持更多动态壁纸格式。

## 5. 名词解释

*   **Labubu：** 指 POPO MART 推出的潮玩 IP 形象。
*   **静态壁纸：** 指静止的图片格式壁纸 (如 JPG, PNG, WebP)。
*   **动态壁纸 (Live Wallpaper)：** 指包含动画或视频的壁纸格式 (MVP 阶段主要为 MP4)。
*   **MVP：** Minimum Viable Product，最小可行产品。