# MVP 前端页面实现 - Todolist

本任务清单根据 `MVP 前端页面方案.md` 生成，用于跟踪壁纸列表页的开发进度。

## Phase 1: 创建核心UI组件

这些是构成页面的基础模块，我们将首先独立创建它们。

- [ ] **1. 创建 `FilterBar.tsx` 组件**
    - [ ] 创建文件 `components/blocks/wallpaper/FilterBar.tsx`。
    - [ ] 标记为客户端组件 (`"use client";`)。
    - [ ] 使用 Shadcn UI 的 `ToggleGroup` 构建"壁纸类型"和"设备适配"的筛选器。
    - [ ] 引入并使用 `useSearchParams`, `useRouter`, `usePathname` 钩子。
    - [ ] 实现当用户点击筛选选项时，能正确更新 URL 查询参数的逻辑。

- [ ] **2. 创建 `WallpaperGrid.tsx` 组件**
    - [ ] 创建文件 `components/blocks/wallpaper/WallpaperGrid.tsx`。
    - [ ] 确保其为服务器组件。
    - [ ] 组件接收一个 `wallpapers` 数组作为 props。
    - [ ] 使用 Tailwind CSS 实现响应式网格布局 (e.g., `grid-cols-2 md:grid-cols-4`)。
    - [ ] 遍历传入的 `wallpapers` 数据，为每一项渲染一个 `WallpaperCard` 组件。
    - [ ] 当 `wallpapers` 数组为空时，显示"无结果"的提示信息。

- [ ] **3. 创建 `WallpaperCard.tsx` 组件**
    - [ ] 创建文件 `components/blocks/wallpaper/WallpaperCard.tsx`。
    - [ ] 标记为客户端组件 (`"use client";`)。
    - [ ] 组件接收一个 `wallpaper` 对象作为 props。
    - [ ] 使用 Shadcn UI 的 `Card` 作为基础布局。
    - [ ] 使用 Next.js 的 `<Link>` 组件将整个卡片包裹，使其能够跳转到详情页 (`/wallpaper/[id]`)。
    - [ ] 实现静态壁纸的缩略图展示 (`next/image`)。
    - [ ] 实现动态壁纸的特殊展示：
        - [ ] 使用 Shadcn UI 的 `Badge` 在角落显示 "LIVE" 标签。
        - [ ] 默认显示海报图。
        - [ ] 实现鼠标悬停 (`onMouseEnter`/`onMouseLeave`)时，自动静音循环播放预览视频。
    - [ ] 在卡片底部显示壁纸标题。

- [ ] **4. 创建 `Pagination.tsx` 组件**
    - [ ] 创建文件 `components/blocks/wallpaper/Pagination.tsx`。
    - [ ] 标记为客户端组件 (`"use client";`)。
    - [ ] 组件接收一个 `pagination` 对象 (`{ current_page, total_pages }`) 作为 props。
    - [ ] 使用 Shadcn UI 的 `Pagination` 组件族进行构建。
    - [ ] 实现逻辑，在生成分页链接时，能够保留 URL 中已有的其他筛选参数 (如 `type`, `device`)。

## Phase 2: 集成与改造首页

完成基础组件后，我们将它们集成到主页面中。

- [ ] **5. 修改首页 `page.tsx`**
    - [ ] 打开 `app/[locale]/(default)/page.tsx` 文件。
    - [ ] 确保其为服务器组件（无 `"use client";` 标记）。
    - [ ] 添加数据获取逻辑：
        - [ ] 从 `searchParams` prop 中读取查询参数。
        - [ ] 调用 `/api/wallpapers` 接口并传入参数以获取壁纸数据和分页信息。
    - [ ] 在现有页面布局中，集成新创建的组件。
        - [ ] 在主内容区顶部引入 `<FilterBar />`。
        - [ ] 接着引入 `<WallpaperGrid />`，并传入获取到的 `wallpapers` 数据。
        - [ ] 在列表下方引入 `<Pagination />`，并传入获取到的 `pagination` 数据。
    - [ ] （可选）根据需要，暂时隐藏或移除原有的营销模块（如 Hero, Features 等）。

## Phase 3: 联调与测试

- [ ] **6. 功能验证**
    - [ ] 验证筛选功能是否正常工作，URL 是否正确更新，壁纸列表是否相应变化。
    - [ ] 验证分页功能，翻页时筛选条件是否保留。
    - [ ] 验证动态壁纸卡片的悬停播放效果。
    - [ ] 验证点击壁纸卡片是否能正确跳转到详情页。
    - [ ] 检查响应式布局在不同屏幕尺寸下的表现。

- [x] 创建 PRD 文档 (`Homepage_Optimization_PRD.md`)。
- [x] 创建技术方案文档 (`Homepage_Optimization_Tech_Spec.md`)。

---

## 首页布局优化实施计划 (Homepage Layout Optimization Implementation)

严格按照 `Homepage_Optimization_Tech_Spec.md` 中的实施步骤执行：

### 步骤1: 创建组件 (Create Components)
- [x] **1.1 创建 `components/blocks/hero/HomepageHero.tsx`**
    - [x] 使用 `shadcn/ui` 的 `Carousel` 组件
    - [x] 支持自动轮播和手动切换
    - [x] 接收 `wallpapers: Wallpaper[]` 作为 props
    - [x] 实现全宽轮播布局，每张图片配标题
    
- [x] **1.2 创建 `components/blocks/wallpaper/WallpaperGridSection.tsx`**
    - [x] 接收 `title: string`, `wallpapers: Wallpaper[]`, `viewAllHref: string` 作为 props
    - [x] 实现带标题和"查看全部"链接的网格布局
    - [x] 复用现有的 `WallpaperCard` 组件
    - [x] 响应式设计：桌面4列，平板3列，移动2列

### 步骤2: 实现组件逻辑 (Implement Component Logic)
- [x] **2.1 完善 `HomepageHero.tsx` 组件**
    - [x] 安装并配置 `embla-carousel-autoplay` 依赖
    - [x] 实现轮播图的自动播放功能
    - [x] 添加鼠标悬停暂停功能
    - [x] 确保图片链接到正确的详情页 `/wallpaper/[id]`
    
- [x] **2.2 完善 `WallpaperGridSection.tsx` 组件**
    - [x] 实现网格布局样式
    - [x] 添加区域标题显示
    - [x] 实现"查看全部"按钮及其链接
    - [x] 确保响应式布局正确

### 步骤3: 重构首页 (Refactor Homepage)
- [x] **3.1 修改 `app/[locale]/(default)/page.tsx`**
    - [x] 移除旧的列表逻辑
    - [x] 实现并行数据获取：热门壁纸和最新壁纸
    - [x] 从最新壁纸中选取前3个作为轮播数据
    - [x] 引入并使用新创建的组件
    - [x] 按照PRD要求的布局顺序排列组件

### 步骤4: 创建列表页 (Create Wallpapers List Page)
- [x] **4.1 新建 `app/[locale]/(default)/wallpapers/page.tsx`**
    - [x] 支持 `sort` 查询参数 (`popular` 或 `newest`)
    - [x] 实现基于参数的数据排序
    - [x] 复用现有的壁纸列表组件
    - [x] 更新 `sitemap.xml` 添加 `/wallpapers` 路由

### 步骤5: 样式微调 (Style Refinement)
- [x] **5.1 优化 `WallpaperCard` 组件**
    - [x] 添加鼠标悬停动效 (hover effects)
    - [x] 实现平滑的放大或阴影效果
    - [x] 使用 `group-hover` 和 `transition` 类
    
- [x] **5.2 整体页面样式调整**
    - [x] 确保各区域间距符合Apple设计风格
    - [x] 实现清晰的视觉层级
    - [x] 添加适当的留白和间距

### 步骤6: 测试验证 (Testing & Validation)
- [ ] **6.1 功能测试**
    - [ ] 验证轮播图自动播放和手动切换
    - [ ] 验证"查看全部"按钮跳转正确
    - [ ] 验证壁纸卡片点击跳转到详情页
    - [ ] 验证数据获取和显示正确
    
- [ ] **6.2 响应式测试**
    - [ ] 测试桌面端4列布局
    - [ ] 测试平板端3列布局  
    - [ ] 测试移动端2列布局
    - [ ] 验证各断点下的视觉效果 