# Labubu 壁纸网站 - 项目文档总览

## 📋 文档目录

本文档集合全面分析了 Labubu 壁纸网站项目的核心功能、技术架构、API 接口、部署运维和开发指南。

### 📚 文档列表

1. **[项目核心功能分析](./项目核心功能分析.md)**
   - 项目概述与技术栈
   - 核心功能模块详解
   - 页面架构与路由设计
   - 数据库设计
   - 项目特色与创新点

2. **[技术架构深度分析](./技术架构深度分析.md)**
   - 整体架构概览
   - 前端架构设计
   - 后端架构设计
   - AI 功能架构
   - 数据库架构
   - 安全架构
   - 性能优化架构

3. **[API 接口文档](./API接口文档.md)**
   - API 概览与响应格式
   - 认证相关接口
   - 支付相关接口
   - AI 功能接口
   - 错误码说明
   - SDK 和示例代码

4. **[部署运维指南](./部署运维指南.md)**
   - 部署概览
   - 环境配置
   - Vercel 部署
   - Cloudflare Workers 部署
   - Docker 部署
   - 监控与日志
   - 故障排除

5. **[开发指南](./开发指南.md)**
   - 开发环境搭建
   - 项目结构详解
   - 开发规范
   - 开发工作流
   - 常用开发任务
   - 调试技巧
   - 最佳实践

## 🚀 项目概述

### 项目定位
**Labubu 壁纸网站** 是一个基于 Next.js 15 构建的现代化 AI SaaS 应用，专注于提供高质量的 Labubu 主题壁纸下载服务。项目采用 ShipAny Template One 作为基础模板，集成了完整的用户系统、支付系统、AI 功能和内容管理系统。

### 核心特性
- 🎨 **AI 驱动**: 集成多个 AI 提供商，支持文本、图像、视频生成
- 🌍 **国际化**: 支持多语言，完整的 i18n 解决方案
- 💳 **支付集成**: 完整的 Stripe 支付流程和积分系统
- 🔐 **安全认证**: NextAuth.js 多提供商认证
- 📱 **响应式设计**: 完美适配各种设备
- ⚡ **高性能**: 现代化架构，优秀的加载速度
- 🚀 **易部署**: 支持多种部署方案

### 技术栈
```
Frontend:  Next.js 15 + React 19 + TypeScript
UI:        Tailwind CSS 4 + Shadcn/UI
Backend:   Next.js API Routes + Supabase
Auth:      NextAuth.js 5.0
Payment:   Stripe
AI:        AI SDK (OpenAI, DeepSeek, Kling, etc.)
Deploy:    Vercel / Cloudflare Workers
Storage:   Cloudflare R2
Database:  Supabase (PostgreSQL)
```

## 🏗️ 架构概览

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    CDN & Edge Network                       │
│                  (Cloudflare/Vercel)                       │
├─────────────────────────────────────────────────────────────┤
│                    Next.js 15 Application                  │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Frontend      │  │   API Routes    │  │  Server      │ │
│  │   (React 19)    │  │   (Serverless)  │  │  Components  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    External Services                        │
│  ┌──────────────┐  ┌──────────────┐  ┌─────────────────────┐ │
│  │   Supabase   │  │   Stripe     │  │   AI Providers      │ │
│  │  (Database)  │  │  (Payment)   │  │  (OpenAI, Kling,    │ │
│  │              │  │              │  │   DeepSeek, etc.)   │ │
│  └──────────────┘  └──────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 核心模块
1. **用户认证系统**: NextAuth.js 多提供商认证
2. **积分与支付系统**: Stripe 集成的完整支付流程
3. **AI 功能模块**: 多提供商 AI 服务集成
4. **内容管理系统**: 壁纸内容的管理和展示
5. **国际化系统**: next-intl 多语言支持

## 📊 核心功能

### 用户功能
- ✅ 多种方式登录 (Google, GitHub, One Tap)
- ✅ 用户资料管理
- ✅ 积分系统和充值
- ✅ 订单历史查看
- ✅ API 密钥管理

### AI 功能
- ✅ 文本生成 (OpenAI, DeepSeek, OpenRouter)
- ✅ 图像生成 (DALL-E, Replicate, Kling)
- ✅ 视频生成 (Kling AI)
- ✅ 流式响应支持
- ✅ 多提供商切换

### 壁纸功能
- ✅ 壁纸浏览和筛选
- ✅ 静态和动态壁纸支持
- ✅ 设备适配 (桌面/移动)
- ✅ 下载统计
- ✅ 轮播展示

### 管理功能
- ✅ 内容管理 (通过数据库)
- ✅ 用户管理
- ✅ 订单管理
- ✅ 反馈管理

## 🛠️ 开发快速开始

### 环境要求
- Node.js 18+
- pnpm 8+
- Git

### 快速启动
```bash
# 1. 克隆项目
git clone https://github.com/your-username/labubuwallpics.git
cd labubuwallpics

# 2. 安装依赖
pnpm install

# 3. 配置环境变量
cp .env.example .env.local
# 编辑 .env.local 填入必要配置

# 4. 启动开发服务器
pnpm dev
```

### 主要脚本
```bash
pnpm dev          # 启动开发服务器
pnpm build        # 构建生产版本
pnpm start        # 启动生产服务器
pnpm lint         # 代码检查
pnpm type-check   # TypeScript 类型检查
```

## 🚀 部署选项

### 1. Vercel (推荐)
- 一键部署
- 自动 CI/CD
- 全球 CDN
- 零配置 HTTPS

### 2. Cloudflare Workers
- 边缘计算
- 全球分布
- 高性能
- 成本效益

### 3. Docker
- 容器化部署
- 自托管选项
- 环境一致性
- 易于扩展

## 📈 性能特性

### 前端性能
- ⚡ Next.js 15 App Router
- 🔄 自动代码分割
- 🖼️ 图片优化 (Next.js Image)
- 💾 智能缓存策略
- 📱 响应式设计

### 后端性能
- 🚀 Serverless 架构
- 🗄️ 数据库查询优化
- 🔄 API 响应缓存
- 📊 性能监控
- 🛡️ 错误处理

## 🔒 安全特性

### 认证安全
- 🔐 OAuth 2.0 标准
- 🎫 JWT Token 管理
- 🔑 API Key 认证
- 👤 会话管理

### 数据安全
- 🛡️ 输入验证
- 🔒 SQL 注入防护
- 🌐 HTTPS 强制
- 🔐 环境变量保护

## 📋 API 概览

### 认证接口
- `POST /api/get-user-info` - 获取用户信息
- `POST /api/get-user-credits` - 获取用户积分

### 支付接口
- `POST /api/checkout` - 创建支付订单
- `GET /pay-success/[session_id]` - 支付成功处理

### AI 接口
- `POST /api/demo/gen-text` - 文本生成
- `POST /api/demo/gen-image` - 图像生成
- `POST /api/demo/gen-video` - 视频生成
- `POST /api/demo/gen-stream-text` - 流式文本生成

### 工具接口
- `POST /api/ping` - 连通性测试
- `POST /api/add-feedback` - 提交反馈

## 🌍 国际化支持

### 支持语言
- 🇺🇸 English (en)
- 🇨🇳 中文 (zh)

### 特性
- 🔄 自动语言检测
- 🌐 动态路由本地化
- 📝 完整的翻译管理
- 🎯 SEO 友好的多语言 URL

## 📊 监控与分析

### 性能监控
- 📈 Web Vitals 监控
- ⚡ 页面加载速度
- 🔍 错误追踪
- 📊 用户行为分析

### 业务监控
- 💰 支付成功率
- 👥 用户增长
- 🎯 功能使用率
- 📈 积分消费统计

## 🤝 贡献指南

### 开发流程
1. Fork 项目
2. 创建功能分支
3. 提交代码
4. 创建 Pull Request
5. 代码审查
6. 合并代码

### 代码规范
- TypeScript 严格模式
- ESLint + Prettier
- 统一的提交消息格式
- 完整的类型定义

## 📞 支持与联系

### 文档支持
- 📖 完整的开发文档
- 🔧 API 接口文档
- 🚀 部署指南
- 💡 最佳实践

### 社区支持
- 💬 GitHub Issues
- 📧 邮件支持
- 💬 Discord 社区

## 📄 许可证

本项目采用 [ShipAny AI SaaS Boilerplate License Agreement](../LICENSE) 许可证。

---

## 🎯 下一步计划

### 短期目标
- [ ] 完善壁纸管理后台
- [ ] 增加更多 AI 提供商
- [ ] 优化移动端体验
- [ ] 添加用户收藏功能

### 长期目标
- [ ] 用户上传壁纸功能
- [ ] 社区评论系统
- [ ] 个性化推荐
- [ ] 移动应用开发

---

**快速导航**:
- [🚀 快速开始](#-开发快速开始)
- [📚 完整文档](#-文档列表)
- [🛠️ API 文档](./API接口文档.md)
- [🚀 部署指南](./部署运维指南.md)
- [💻 开发指南](./开发指南.md)
