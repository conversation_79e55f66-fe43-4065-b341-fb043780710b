# Labubu 壁纸网站 - 技术架构深度分析

## 1. 整体架构概览

### 1.1 架构模式
项目采用 **现代化全栈架构**，基于 Next.js 15 的 App Router 模式，实现了前后端一体化开发：

```
┌─────────────────────────────────────────────────────────────┐
│                    Cloudflare Workers                       │
│                   (部署运行环境)                              │
├─────────────────────────────────────────────────────────────┤
│                    Next.js 15 App                          │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Frontend      │  │   API Routes    │  │  Server      │ │
│  │   (React 19)    │  │   (Route        │  │  Components  │ │
│  │                 │  │   Handlers)     │  │              │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    External Services                        │
│  ┌──────────────┐  ┌──────────────┐  ┌─────────────────────┐ │
│  │   Supabase   │  │   Stripe     │  │   AI Providers      │ │
│  │  (Database)  │  │  (Payment)   │  │  (OpenAI, Kling,    │ │
│  │              │  │              │  │   DeepSeek, etc.)   │ │
│  └──────────────┘  └──────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 1.2 技术选型理由

#### Next.js 15 + App Router
- **服务端渲染 (SSR)**: 提升 SEO 和首屏加载速度
- **静态生成 (SSG)**: 缓存优化，降低服务器压力
- **API Routes**: 统一的全栈开发体验
- **自动代码分割**: 优化包体积和加载性能

#### React 19
- **并发特性**: 提升用户交互响应性
- **服务端组件**: 减少客户端 JavaScript 体积
- **Suspense**: 优雅的异步数据加载处理

#### Supabase
- **实时数据库**: PostgreSQL + 实时订阅
- **内置认证**: 减少认证系统开发复杂度
- **Row Level Security**: 数据安全保障
- **自动 API 生成**: 快速开发数据接口

## 2. 前端架构设计

### 2.1 组件架构

#### 组件分层设计
```
components/
├── ui/                    # 基础 UI 组件 (Shadcn/UI)
│   ├── button.tsx
│   ├── dialog.tsx
│   └── ...
├── blocks/               # 业务区块组件
│   ├── hero/            # 首页 Hero 区域
│   ├── pricing/         # 定价组件
│   ├── wallpaper/       # 壁纸相关组件
│   └── ...
├── console/             # 控制台组件
├── feedback/            # 反馈组件
└── ...
```

#### 组件设计原则
1. **单一职责**: 每个组件只负责一个功能
2. **可复用性**: 通过 props 实现组件的灵活配置
3. **类型安全**: 完整的 TypeScript 类型定义
4. **性能优化**: 合理使用 React.memo 和 useMemo

### 2.2 状态管理

#### Context API 使用
```typescript
// contexts/app.tsx
interface AppContextType {
  user: User | null;
  credits: UserCredits | null;
  // ... 其他全局状态
}

const AppContext = createContext<AppContextType | undefined>(undefined);
```

#### 状态管理策略
- **全局状态**: 使用 React Context (用户信息、积分等)
- **组件状态**: 使用 useState/useReducer
- **服务端状态**: 通过 API 调用获取，客户端缓存
- **表单状态**: 使用 react-hook-form

### 2.3 路由设计

#### App Router 结构
```
app/
├── [locale]/                    # 国际化路由层
│   ├── (default)/              # 路由组 - 默认布局
│   │   ├── page.tsx            # 首页
│   │   ├── wallpapers/         # 壁纸列表
│   │   │   └── page.tsx
│   │   ├── (console)/          # 路由组 - 控制台布局
│   │   │   ├── my-orders/
│   │   │   ├── my-credits/
│   │   │   └── api-keys/
│   │   └── layout.tsx          # 默认布局
│   ├── pay-success/            # 支付成功
│   └── layout.tsx              # 语言布局
├── api/                        # API 路由
│   ├── checkout/
│   ├── demo/
│   └── ...
└── layout.tsx                  # 根布局
```

#### 路由特性
- **嵌套布局**: 支持多层布局嵌套
- **路由组**: 使用 `()` 实现布局分组
- **动态路由**: `[locale]` 实现国际化
- **并行路由**: 支持同时渲染多个页面

## 3. 后端架构设计

### 3.1 API 设计模式

#### Route Handlers 架构
```typescript
// app/api/[endpoint]/route.ts
export async function POST(req: Request) {
  try {
    // 1. 参数验证
    const { param1, param2 } = await req.json();
    if (!param1) {
      return respErr("invalid params");
    }

    // 2. 权限检查
    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    // 3. 业务逻辑处理
    const result = await businessLogic(param1, param2);

    // 4. 统一响应格式
    return respData(result);
  } catch (e) {
    console.log("api error:", e);
    return respErr("operation failed");
  }
}
```

#### 统一响应格式
```typescript
// lib/resp.ts
export function respData(data: any) {
  return Response.json({ code: 0, data, message: "success" });
}

export function respErr(message: string) {
  return Response.json({ code: -1, message }, { status: 400 });
}
```

### 3.2 数据访问层

#### 模型层设计
```
models/
├── db.ts              # 数据库连接
├── user.ts            # 用户模型
├── order.ts           # 订单模型
├── credit.ts          # 积分模型
├── post.ts            # 内容模型
└── ...
```

#### 数据访问模式
```typescript
// models/user.ts
export async function findUserByEmail(email: string): Promise<User | undefined> {
  const supabase = getSupabaseClient();
  const { data, error } = await supabase
    .from("users")
    .select("*")
    .eq("email", email)
    .limit(1)
    .single();

  if (error) {
    return undefined;
  }
  return data;
}
```

### 3.3 业务逻辑层

#### 服务层架构
```
services/
├── user.ts           # 用户服务
├── credit.ts         # 积分服务
├── order.ts          # 订单服务
├── page.ts           # 页面服务
└── ...
```

#### 业务逻辑封装
```typescript
// services/credit.ts
export async function decreaseCredits({
  user_uuid,
  trans_type,
  credits,
}: {
  user_uuid: string;
  trans_type: CreditsTransType;
  credits: number;
}) {
  // 1. 检查用户积分余额
  const userCredits = await getUserCredits(user_uuid);
  if (userCredits.left_credits < credits) {
    throw new Error("insufficient credits");
  }

  // 2. 创建消费记录
  const credit: Credit = {
    trans_no: getSnowId(),
    user_uuid,
    trans_type,
    credits: -credits,
    created_at: getIsoTimestr(),
    expired_at: getOneYearLaterTimestr(),
  };

  await insertCredit(credit);
}
```

## 4. AI 功能架构

### 4.1 AI SDK 设计

#### 提供商抽象层
```typescript
// aisdk/provider/video-model/v1/video-model-v1.ts
export interface VideoModelV1 {
  readonly specificationVersion: "v1";
  readonly provider: string;
  readonly modelId: string;
  readonly maxVideosPerCall?: number;

  doGenerate(options: VideoModelV1CallOptions): Promise<VideoModelV1CallResult>;
}
```

#### 统一调用接口
```typescript
// aisdk/generate-video/generate-video.ts
export async function generateVideo({
  model,
  prompt,
  n = 1,
  providerOptions,
}: {
  model: VideoModelV1;
  prompt: string;
  n?: number;
  providerOptions?: Record<string, Record<string, JSONValue>>;
}): Promise<GenerateVideoResult>
```

### 4.2 多提供商支持

#### 提供商配置
```typescript
// app/api/demo/gen-text/route.ts
let textModel: LanguageModelV1;

switch (provider) {
  case "openai":
    textModel = openai(model);
    break;
  case "deepseek":
    textModel = deepseek(model);
    break;
  case "openrouter":
    const openrouter = createOpenRouter({
      apiKey: process.env.OPENROUTER_API_KEY,
    });
    textModel = openrouter(model);
    break;
  // ... 其他提供商
}
```

#### 特殊功能处理
```typescript
// DeepSeek R1 推理提取
if (model === "deepseek/deepseek-r1") {
  const enhancedModel = wrapLanguageModel({
    model: textModel,
    middleware: extractReasoningMiddleware({
      tagName: "think",
    }),
  });
  textModel = enhancedModel;
}
```

## 5. 数据库架构

### 5.1 数据模型设计

#### 核心实体关系
```
Users (用户)
├── Orders (订单) - 1:N
├── Credits (积分) - 1:N
├── ApiKeys (API密钥) - 1:N
└── Affiliates (联盟) - 1:N

Orders (订单)
└── Credits (积分) - 1:1

Posts (内容)
└── 多语言支持
```

#### 表结构设计要点
1. **UUID 主键**: 使用 UUID 避免 ID 猜测攻击
2. **时间戳**: 统一使用 timestamptz 类型
3. **软删除**: 通过 status 字段实现
4. **索引优化**: 在查询频繁的字段上建立索引

### 5.2 数据访问优化

#### 查询优化策略
```typescript
// 分页查询优化
export async function getUsers(page: number = 1, limit: number = 50) {
  const offset = (page - 1) * limit;
  const { data, error } = await supabase
    .from("users")
    .select("*")
    .order("created_at", { ascending: false })
    .range(offset, offset + limit - 1);  // 使用 range 而不是 limit/offset
}
```

#### 缓存策略
- **查询缓存**: Supabase 自动查询缓存
- **应用缓存**: 使用 `lib/cache.ts` 实现客户端缓存
- **CDN 缓存**: Cloudflare CDN 静态资源缓存

## 6. 安全架构

### 6.1 认证安全

#### NextAuth.js 配置
```typescript
// auth/config.ts
export const authConfig: NextAuthConfig = {
  providers: [
    GoogleProvider({
      clientId: process.env.AUTH_GOOGLE_ID,
      clientSecret: process.env.AUTH_GOOGLE_SECRET,
    }),
    // 自定义 Google One Tap 提供商
    CredentialsProvider({
      id: "google-one-tap",
      name: "Google One Tap",
      credentials: { credential: { type: "text" } },
      async authorize(credentials) {
        // Token 验证逻辑
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user, account }) {
      // JWT Token 处理
    },
    async session({ session, token }) {
      // Session 数据处理
    },
  },
};
```

### 6.2 API 安全

#### 权限验证中间件
```typescript
// services/user.ts
export async function getUserUuid(): Promise<string | null> {
  try {
    // 1. 检查 NextAuth Session
    const session = await auth();
    if (session?.user?.uuid) {
      return session.user.uuid;
    }

    // 2. 检查 API Key (用于 API 调用)
    const headersList = await headers();
    const apiKey = headersList.get("x-api-key");
    if (apiKey) {
      return await getUserUuidByApiKey(apiKey);
    }

    return null;
  } catch (e) {
    return null;
  }
}
```

### 6.3 数据安全

#### 环境变量管理
```typescript
// 敏感信息通过环境变量管理
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
const stripeKey = process.env.STRIPE_PRIVATE_KEY;
```

#### 输入验证
```typescript
// 统一参数验证
export async function POST(req: Request) {
  const { param1, param2 } = await req.json();
  
  // 参数验证
  if (!param1 || typeof param1 !== 'string') {
    return respErr("invalid params");
  }
  
  // 业务逻辑...
}
```

## 7. 性能优化架构

### 7.1 前端性能优化

#### 代码分割策略
- **路由级分割**: App Router 自动实现
- **组件级分割**: 使用 `React.lazy()` 和 `Suspense`
- **第三方库分割**: 动态导入大型依赖

#### 图片优化
```typescript
// Next.js Image 组件优化
<Image
  src={wallpaper.thumbnail_url}
  alt={wallpaper.title}
  width={300}
  height={200}
  loading="lazy"
  placeholder="blur"
  blurDataURL="data:image/jpeg;base64,..."
/>
```

### 7.2 后端性能优化

#### 数据库查询优化
- **索引优化**: 在频繁查询字段上建立索引
- **查询优化**: 使用 `select` 指定需要的字段
- **分页优化**: 使用 `range` 而不是 `limit/offset`

#### 缓存策略
```typescript
// lib/cache.ts - 客户端缓存
export function cacheSet(key: string, value: any, expiresAt: number) {
  const item = {
    value,
    expiresAt,
  };
  localStorage.setItem(key, JSON.stringify(item));
}
```

## 8. 部署架构

### 8.1 Cloudflare Workers 部署

#### OpenNext 适配器
```typescript
// open-next.config.ts
import type { OpenNextConfig } from "@opennextjs/cloudflare";

const config: OpenNextConfig = {
  default: {
    override: {
      wrapper: "cloudflare-node",
      converter: "edge",
      incrementalCache: "cloudflare-kv",
      tagCache: "cloudflare-kv",
      queue: "cloudflare-queue",
    },
  },
};

export default config;
```

#### 环境配置
```toml
# wrangler.toml
name = "labubuwallpics"
compatibility_date = "2024-01-01"

[vars]
NEXT_PUBLIC_WEB_URL = "https://example.com"
SUPABASE_URL = "https://xxx.supabase.co"
# ... 其他环境变量
```

### 8.2 多环境支持

#### 环境配置管理
- **开发环境**: `.env.local`
- **生产环境**: `.env.production`
- **Cloudflare**: `wrangler.toml` 中的 `[vars]`

#### CI/CD 流程
```bash
# 构建和部署脚本
npm run cf:build    # 构建 Cloudflare 版本
npm run cf:deploy   # 部署到 Cloudflare Workers
```

## 9. 监控与运维

### 9.1 错误处理

#### 统一错误处理
```typescript
// 全局错误捕获
export async function POST(req: Request) {
  try {
    // 业务逻辑
  } catch (e) {
    console.log("api error:", e);
    return respErr("operation failed");
  }
}
```

### 9.2 日志记录

#### 结构化日志
```typescript
// 关键操作日志记录
console.log("user action:", {
  user_uuid,
  action: "purchase_credits",
  amount: credits,
  timestamp: new Date().toISOString(),
});
```

## 10. 总结

该项目的技术架构具有以下特点：

1. **现代化**: 采用最新的 Next.js 15 和 React 19
2. **模块化**: 清晰的分层架构和组件设计
3. **可扩展**: 支持多种 AI 提供商和部署方案
4. **高性能**: 多层缓存和优化策略
5. **安全性**: 完善的认证和权限控制
6. **可维护**: 统一的代码规范和错误处理

这种架构设计为快速开发和长期维护提供了良好的基础，特别适合需要集成多种外部服务的现代 Web 应用。
