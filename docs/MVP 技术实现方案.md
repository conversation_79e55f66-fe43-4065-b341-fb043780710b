# Labubu 壁纸网站 - MVP 技术实现方案 (Next.js 15 App Router, Route Handlers, CF Workers)

## 1. 技术栈概述

*   **前端框架/元框架：** Next.js 15 (App Router)
*   **React 版本：** React 19
*   **UI 库/CSS方案：** Tailwind CSS 4 (alpha/beta)
*   **部署适配器：** OpenNext
*   **部署平台：** Cloudflare Workers (通过 OpenNext)
*   **数据库：** Supabase (PostgreSQL)
*   **对象存储：** Cloudflare R2
*   **图片/视频处理 (上传时管理员操作)：** `sharp` (静态图片)，`ffmpeg` (或其 Node.js 包装如 `fluent-ffmpeg`，用于动态壁纸元数据/预览提取)

## 2. 数据库设计 (Supabase - PostgreSQL)

### 2.1 `wallpapers` 表结构

```sql
CREATE TABLE labubu_wallpapers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title TEXT NOT NULL,
    description TEXT, -- 可选描述
    
    -- 壁纸类型与设备适配
    wallpaper_type VARCHAR(10) NOT NULL DEFAULT 'static', -- 'static', 'live'
    device_target VARCHAR(10) NOT NULL DEFAULT 'any',  -- 'mobile', 'desktop', 'any'

    -- R2 Object Keys
    -- 静态壁纸相关
    static_image_original_key TEXT,  -- 原始静态图片在 R2 的 Key
    static_image_preview_key TEXT,   -- 用于详情页的大预览图 Key (优化后)
    static_image_thumbnail_key TEXT, -- 用于列表页的缩略图 Key (优化后)

    -- 动态壁纸相关
    live_video_key TEXT,             -- 动态壁纸视频文件 (MP4) 在 R2 的 Key
    live_poster_image_key TEXT,      -- 动态壁纸的封面/海报图 Key
    live_thumbnail_video_key TEXT,   -- (可选) 专门为列表页悬停预览生成的短循环、低码率视频 Key

    -- 元数据
    width INT,                       -- 原始宽度
    height INT,                      -- 原始高度
    file_size_bytes BIGINT,          -- 原始文件大小 (近似值)
    format VARCHAR(10),              -- 原始文件格式 (e.g., 'jpeg', 'png', 'mp4')
    duration_seconds INT,            -- 动态壁纸时长 (秒)

    tags TEXT[],                     -- 标签数组 (未来扩展用，MVP 可为空)
    download_count INT DEFAULT 0,    -- 下载次数 (未来扩展用)
    is_published BOOLEAN DEFAULT TRUE, -- 是否发布
    
    -- uploaded_by UUID,             -- 上传者ID (如果未来有用户系统)
    -- FOREIGN KEY (uploaded_by) REFERENCES auth.users(id) ON DELETE SET NULL;
    
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL
);
```

### 2.2 RLS (Row Level Security)

```sql
ALTER TABLE labubu_wallpapers ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Public read access for published wallpapers"
ON labubu_wallpapers
FOR SELECT
USING (is_published = TRUE);

-- 管理员写入/修改/删除策略 (MVP 阶段主要通过 service_role key 操作，此策略为后续图形化后台预留)
-- CREATE POLICY "Admin full access"
-- ON wallpapers
-- FOR ALL
-- USING (auth.role() = 'admin_role_name'); -- 假设有一个 admin_role_name
-- 或者直接使用 service_role key 跳过 RLS (不推荐用于客户端直接调用的 API)
```

### 2.3 索引建议

```sql
CREATE INDEX idx_labubu_wallpapers_type ON labubu_wallpapers(wallpaper_type);
CREATE INDEX idx_labubu_wallpapers_device ON labubu_wallpapers(device_target);
CREATE INDEX idx_labubu_wallpapers_tags ON labubu_wallpapers USING GIN (tags);
CREATE INDEX idx_labubu_wallpapers_created_at_desc ON labubu_wallpapers(created_at DESC);
```

**Supabase 配置：**
*   在 Supabase 项目中创建此表。
*   获取 Supabase 项目的 URL, `anon` key, 和 `service_role` key。

## 3. 对象存储 (Cloudflare R2)

*   **Bucket 设置：**
    *   创建一个 R2 Bucket (例如 `labubu-walls-media`)。
    *   **绑定自定义域名：** 例如 `media.yourlabubuwalls.com`。确保该域名通过 Cloudflare CDN (橙色云朵)。
*   **文件组织结构 (建议)：**
    *   `static/originals/<uuid_or_unique_id>/filename.jpg`
    *   `static/previews/<uuid_or_unique_id>/filename.webp`
    *   `static/thumbnails/<uuid_or_unique_id>/filename.webp`
    *   `live/videos/<uuid_or_unique_id>/filename.mp4`
    *   `live/posters/<uuid_or_unique_id>/filename.webp`
    *   `live/thumbnails/<uuid_or_unique_id>/filename.mp4`
*   **R2 API 凭证：**
    *   创建 R2 API 令牌 (读写权限)。
    *   将 `Access Key ID`, `Secret Access Key`, `Account ID` 作为 Secrets 存储。

## 4. 后端实现 (Next.js App Router - Route Handlers)

### 4.1 环境变量与 Secrets (Cloudflare Workers 环境)
*   `NEXT_PUBLIC_SUPABASE_URL` (Env)
*   `NEXT_PUBLIC_SUPABASE_ANON_KEY` (Env)
*   `SUPABASE_SERVICE_ROLE_KEY` (Secret)
*   `R2_ACCOUNT_ID` (Env or Secret)
*   `R2_ACCESS_KEY_ID` (Secret)
*   `R2_SECRET_ACCESS_KEY` (Secret)
*   `R2_BUCKET_NAME` (Env)
*   `NEXT_PUBLIC_R2_PUBLIC_DOMAIN` (Env) - 例如 `https://media.yourlabubuwalls.com`

### 4.2 Supabase 客户端初始化
*   **`lib/supabase/client.ts` (用于 Client Components):**
    ```typescript
    import { createBrowserClient } from '@supabase/ssr';

    export const createSupabaseBrowserClient = () =>
      createBrowserClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
      );
    ```
*   **`lib/supabase/server.ts` (用于 Server Components & Route Handlers):**
    ```typescript
    import { createServerClient, type CookieOptions } from '@supabase/ssr';
    import { cookies } from 'next/headers'; // or from NextRequest for Route Handlers

    // For Server Components / Server Actions
    export const createSupabaseServerClient = (cookieStore: ReturnType<typeof cookies>) => {
      return createServerClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!, // Use anon key for reads, or service_role for writes
        {
          cookies: {
            get(name: string) {
              return cookieStore.get(name)?.value;
            },
            // set and remove needed if you implement auth and session management
          },
        }
      );
    };
    
    // For Route Handlers (if needing service_role for admin tasks, or a different setup)
    // Direct instantiation with service_role key for admin-like operations if no user context
    import { createClient } from '@supabase/supabase-js';
    export const supabaseAdmin = createClient(
        process.env.NEXT_PUBLIC_SUPABASE_URL!,
        process.env.SUPABASE_SERVICE_ROLE_KEY! // Use service role for privileged operations
    );
    ```
    *Note: For MVP, Route Handlers primarily read data, so `createServerClient` with `anon` key is often sufficient. `supabaseAdmin` would be for future admin panel APIs.*

### 4.3 R2 S3 SDK 客户端初始化 (`lib/r2Client.ts`)
```typescript
import { S3Client } from "@aws-sdk/client-s3";

const R2_ACCOUNT_ID = process.env.R2_ACCOUNT_ID;
const R2_ACCESS_KEY_ID = process.env.R2_ACCESS_KEY_ID;
const R2_SECRET_ACCESS_KEY = process.env.R2_SECRET_ACCESS_KEY;

if (!R2_ACCOUNT_ID || !R2_ACCESS_KEY_ID || !R2_SECRET_ACCESS_KEY) {
    // This check might be better suited for build time or a startup check in OpenNext
    // console.warn("Missing R2 configuration for S3 Client.");
}

export const r2Client = new S3Client({
    region: "auto",
    endpoint: `https://${R2_ACCOUNT_ID}.r2.cloudflarestorage.com`,
    credentials: {
        accessKeyId: R2_ACCESS_KEY_ID!,
        secretAccessKey: R2_SECRET_ACCESS_KEY!,
    },
});
```

### 4.4 API Route Handlers - 设计思路

*   **A. 获取壁纸列表 API (`app/api/wallpapers/route.ts`)**
    *   **HTTP Method:** `GET`
    *   **请求参数 (Query Parameters):**
        *   `page` (number, default: 1): 用于分页。
        *   `limit` (number, default: 20): 每页数量。
        *   `type` (string, optional, e.g., 'static', 'live'): 按壁纸类型筛选。
        *   `device` (string, optional, e.g., 'mobile', 'desktop'): 按设备适配筛选。
        *   `sortBy` (string, optional, default: 'created_at'): 排序字段。
        *   `order` (string, optional, default: 'desc'): 排序方向 ('asc', 'desc')。
        *   `q` (string, optional): 搜索关键词 (用于标题模糊搜索)。
    *   **核心处理逻辑:**
        1.  **解析请求参数：** 从 `NextRequest` 对象中获取并验证查询参数。
        2.  **构建数据库查询：** 使用 Supabase 客户端（通常以匿名用户权限，因为这是公开数据）。
            *   动态构建查询条件以支持 `type`, `device`, 和 `q` 筛选。
            *   应用 `is_published = TRUE` 条件。
            *   应用分页 (`.range(from, to)`)。
            *   应用排序 (`.order(sortBy, { ascending })`)。
            *   请求总数 (`{ count: 'exact' }`) 以便计算总页数。
        3.  **执行数据库查询：** 获取壁纸数据列表和总数。
        4.  **处理结果：**
            *   **拼接 R2 URL：** 遍历查询结果，为每个壁纸的 R2 `_key` 字段（如 `static_image_thumbnail_key`, `live_poster_image_key` 等）结合 `NEXT_PUBLIC_R2_PUBLIC_DOMAIN` 环境变量，生成完整的、可公开访问的图片/视频 URL。这些 URL 将用于前端展示。
            *   **格式化响应：** 组织数据为 JSON 格式，包含壁纸列表 (带完整 URLs) 和分页信息 (当前页、总页数、总条目数)。
        5.  **返回响应：** 使用 `NextResponse.json()` 返回处理后的数据。
    *   **错误处理：** 捕获数据库查询错误或参数错误，并返回适当的 HTTP 状态码和错误信息。

*   **B. 获取单个壁纸详情 API (`app/api/wallpapers/[id]/route.ts`)**
    *   **HTTP Method:** `GET`
    *   **请求参数 (Path Parameter):** `id` (string - 壁纸的 UUID)。
    *   **核心处理逻辑:**
        1.  **解析请求参数：** 从 `params` 对象中获取壁纸 `id`。
        2.  **构建数据库查询：** 使用 Supabase 客户端。
            *   根据 `id` 查询 `wallpapers` 表。
            *   应用 `is_published = TRUE` 条件。
            *   使用 `.single()` 确保只返回一条记录或在未找到时抛出特定错误。
        3.  **执行数据库查询：** 获取单个壁纸数据。
        4.  **处理结果：**
            *   **拼接 R2 URL：** 为查询到的壁纸的所有 R2 `_key` 字段生成完整的公开访问 URL。
            *   **格式化响应：** 组织数据为 JSON 格式。
        5.  **返回响应：** 使用 `NextResponse.json()` 返回处理后的数据。
    *   **错误处理：**
        *   如果未找到对应 `id` 的壁纸，返回 404 Not Found。
        *   捕获其他数据库查询错误，返回适当的 HTTP 状态码和错误信息。

*   **C. 壁纸下载接口 (采用方案1：前端直接链接)**
    *   **思路：** 此方案不涉及后端 API Route Handler 进行文件代理。
    *   前端的下载按钮将直接链接到通过 `NEXT_PUBLIC_R2_PUBLIC_DOMAIN` 和壁纸的原始文件 R2 Key (如 `static_image_original_key` 或 `live_video_key`) 拼接而成的 URL。
    *   `<a>` 标签的 `download` 属性将用于建议浏览器下载文件并指定文件名。
    *   **因此，MVP 阶段不为此功能创建专门的后端下载 Route Handler。**

*   **D. 管理员上传壁纸 API (`app/api/admin/upload-wallpaper/route.ts`)**
    *   **安全注意:** 此 API 应被视为内部接口，不应公开暴露给普通用户。在生产环境中，应通过 IP 白名单、内部网络访问或管理员身份验证（未来版本）进行保护。
    *   **HTTP Method:** `POST`
    *   **请求格式:** `multipart/form-data`
    *   **请求体字段 (Form Data):**
        *   `file`: (File) 必需。原始的静态图片文件。
        *   `title`: (string) 必需。壁纸的标题。
        *   `description`: (string) 可选。壁纸的描述。
        *   `device_target`: (string, 'mobile' | 'desktop' | 'any') 可选，默认为 'any'。
        *   `is_published`: (string, 'true' | 'false') 可选，默认为 'true'。
    *   **核心处理逻辑 (串行与并行结合):**
        1.  **解析请求：** 从 `NextRequest` 中解析 `multipart/form-data`。
        2.  **文件读取：** 将上传的 `File` 对象读取为 `Buffer`。
        3.  **图片处理 (`sharp`):**
            *   使用 `sharp` 加载原始图片 `Buffer`。
            *   调用 `.metadata()` 获取原始图片的 `width`, `height`, `size`, `format` 等元数据。
            *   **生成预览图:** 调用 `.resize()` 和 `.webp()` (或 `.jpeg()`) 生成一个用于详情页的大尺寸预览图 `Buffer` (例如，宽度限制为 1920px)。
            *   **生成缩略图:** 再次调用 `.resize()` 和 `.webp()` 生成一个用于列表页的小尺寸缩略图 `Buffer` (例如，宽度限制为 400px)。
        4.  **定义 R2 存储路径:**
            *   生成一个唯一的 UUID (例如 `fileGroupId`) 来关联这组原始图、预览图和缩略图。
            *   根据您在文档中定义的文件组织结构，为三张图片分别定义 R2 `Object Key`。
                *   `static/originals/<fileGroupId>/<original_filename>`
                *   `static/previews/<fileGroupId>/<original_filename>.webp`
                *   `static/thumbnails/<fileGroupId>/<original_filename>.webp`
        5.  **并行上传到 R2:**
            *   初始化 R2 S3 客户端 (`storage.ts`)。
            *   使用 `Promise.all()` 将原始图片 `Buffer`、预览图 `Buffer` 和缩略图 `Buffer` 并行上传到 R2 对应的路径。
        6.  **写入数据库 (Supabase):**
            *   初始化 Supabase Admin 客户端 (`lib/supabase/admin.ts`)。
            *   准备一个包含所有元数据（标题、描述、尺寸、文件大小、R2 Keys 等）的对象。
            *   调用 `supabaseAdmin.from('labubu_wallpapers').insert(...)` 将这条记录写入数据库。
        7.  **返回响应:**
            *   如果成功，使用 `respData` 返回新创建的数据库记录。
            *   如果过程中任何一步失败（如图片处理、上传、数据库写入），捕获错误，记录日志，并使用 `respErr` 返回清晰的错误信息。

### 4.5 文件上传与处理流程 (MVP 管理员操作)
(与之前方案一致)
1.  准备壁纸文件。
2.  本地预处理 (使用 `sharp` 和 `ffmpeg`/`ffprobe` 脚本)。
3.  手动上传处理后的文件到 R2 (使用 `rclone`, AWS CLI, R2 UI, 或 Node.js 上传脚本)。
4.  将元数据和 R2 Keys 存入 Supabase (使用数据库客户端或 Node.js 脚本调用 `supabaseAdmin` 客户端)。

## 5. 前端实现 (Next.js App Router & React 19)

### 5.1 页面结构 (App Router)
*   `app/layout.tsx`: Root layout (Header, Footer, global styles, Supabase provider if needed for auth later).
*   `app/page.tsx`: 首页 (壁纸列表页) - Server Component.
*   `app/wallpaper/[id]/page.tsx`: 壁纸详情页 - Server Component.

### 5.2 组件
*   `components/Header.tsx` (Client Component for potential interactivity).
*   `components/Footer.tsx` (Server or Client Component).
*   `components/FilterBar.tsx` (Client Component - interacts with URL `searchParams`).
*   `components/WallpaperGrid.tsx` (Server Component - receives data as props).
*   `components/WallpaperCard.tsx` (Client Component for video hover, or Server Component if simplified).
*   `components/Pagination.tsx` (Client Component).
*   `components/VideoPlayer.tsx` (Client Component for video controls in detail page).
*   `components/ImageWithFallback.tsx` (Optional, custom `next/image` wrapper for better error handling or placeholders).

### 5.3 数据获取 (Client Components calling Route Handlers)
*   **列表页 (`app/page.tsx`):**
    *   This page itself is a Server Component. It can fetch initial data.
    *   Client-side data fetching for subsequent loads/filters:
        *   `FilterBar.tsx` and `Pagination.tsx` (Client Components) will use `fetch` to call `/api/wallpapers` with appropriate query parameters.
        *   `useSWR` or `react-query` (`@tanstack/react-query`) can be used for client-side data fetching, caching, and state management.
        *   Alternatively, for filters/pagination, Client Components can update URL `searchParams` via `next/navigation`'s `useRouter().push()` or `replace()`. The `page.tsx` (Server Component) will re-render with new `searchParams` if it's designed to read them for its initial data fetch or if a parent Server Component re-renders. For purely client-driven updates after initial load, `fetch` to the API is more direct.
*   **详情页 (`app/wallpaper/[id]/page.tsx`):**
    *   This page is a Server Component and can fetch its own data.
    *   If any part of the detail page needs client-side fetching (e.g., related items loaded async), it would call `/api/wallpapers/[id]` or a new API.

### 5.4 动态壁纸预览与播放
*   **列表卡片悬停预览 (`WallpaperCard.tsx` - Client Component):**
    *   HTML5 `<video>` with `autoplay loop muted playsinline poster={posterUrl}`.
    *   `useRef` for video element, `onMouseEnter`/`onMouseLeave` to `play()`/`pause()`.
*   **详情页播放 (`VideoPlayer.tsx` - Client Component):**
    *   HTML5 `<video>` with `controls`.

### 5.5 图片展示
*   Use `next/image` component. `src` will be the R2 public URL constructed by the API or directly in Server Components.

### 5.6 下载功能 (方案 1)
*   In `WallpaperCard.tsx` (for quick download if added) and `app/wallpaper/[id]/page.tsx`:
    ```html
    <a 
      href={wallpaper.static_image_original_url || wallpaper.live_video_url} 
      download={`${wallpaper.title}.${wallpaper.format === 'mp4' ? 'mp4' : 'jpg'}`} // Adjust filename/extension
      className="your-download-button-styles"
    >
      Download Wallpaper
    </a>
    ```