# Labubu 壁纸网站 - 部署运维指南

## 1. 部署概览

### 1.1 支持的部署平台
- **Vercel** (推荐) - 一键部署，自动 CI/CD
- **Cloudflare Workers** - 边缘计算，全球加速
- **Docker** - 容器化部署，支持自托管

### 1.2 部署架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    CDN & Edge Network                       │
│                  (Cloudflare/Vercel)                       │
├─────────────────────────────────────────────────────────────┤
│                    Application Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Next.js App   │  │   API Routes    │  │  Static      │ │
│  │   (SSR/SSG)     │  │   (Serverless)  │  │  Assets      │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                    External Services                        │
│  ┌──────────────┐  ┌──────────────┐  ┌─────────────────────┐ │
│  │   Supabase   │  │   Stripe     │  │   AI Providers      │ │
│  │  (Database)  │  │  (Payment)   │  │  (OpenAI, etc.)     │ │
│  └──────────────┘  └──────────────┘  └─────────────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 2. 环境配置

### 2.1 环境变量清单

#### 基础配置
```bash
# 应用基础配置
NEXT_PUBLIC_WEB_URL=https://your-domain.com
NEXT_PUBLIC_APP_NAME=Labubu Wallpapers

# 数据库配置 (Supabase)
SUPABASE_URL=https://xxx.supabase.co
SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# 认证配置 (NextAuth)
NEXTAUTH_SECRET=your-nextauth-secret
NEXTAUTH_URL=https://your-domain.com

# Google OAuth
AUTH_GOOGLE_ID=your-google-client-id
AUTH_GOOGLE_SECRET=your-google-client-secret
NEXT_PUBLIC_AUTH_GOOGLE_ID=your-google-client-id
NEXT_PUBLIC_AUTH_GOOGLE_ENABLED=true

# GitHub OAuth
AUTH_GITHUB_ID=your-github-client-id
AUTH_GITHUB_SECRET=your-github-client-secret
NEXT_PUBLIC_AUTH_GITHUB_ENABLED=true
```

#### 支付配置
```bash
# Stripe 配置
STRIPE_PUBLIC_KEY=pk_live_xxx
STRIPE_PRIVATE_KEY=sk_live_xxx
NEXT_PUBLIC_PAY_SUCCESS_URL=https://your-domain.com/success
NEXT_PUBLIC_PAY_FAIL_URL=https://your-domain.com/fail
NEXT_PUBLIC_PAY_CANCEL_URL=https://your-domain.com/cancel
```

#### AI 服务配置
```bash
# OpenAI
OPENAI_API_KEY=sk-xxx

# DeepSeek
DEEPSEEK_API_KEY=sk-xxx

# OpenRouter
OPENROUTER_API_KEY=sk-or-xxx

# SiliconFlow
SILICONFLOW_API_KEY=sk-xxx
SILICONFLOW_BASE_URL=https://api.siliconflow.cn/v1

# Kling AI
KLING_ACCESS_KEY=your-access-key
KLING_SECRET_KEY=your-secret-key
```

#### 存储配置
```bash
# Cloudflare R2
R2_ACCOUNT_ID=your-account-id
R2_ACCESS_KEY_ID=your-access-key
R2_SECRET_ACCESS_KEY=your-secret-key
R2_BUCKET_NAME=your-bucket-name
R2_PUBLIC_URL=https://your-r2-domain.com
```

### 2.2 环境文件管理

#### 开发环境 (.env.local)
```bash
cp .env.example .env.local
# 编辑 .env.local 填入开发环境配置
```

#### 生产环境 (.env.production)
```bash
cp .env.example .env.production
# 编辑 .env.production 填入生产环境配置
```

## 3. Vercel 部署

### 3.1 一键部署
[![Deploy with Vercel](https://vercel.com/button)](https://vercel.com/new/clone?repository-url=https%3A%2F%2Fgithub.com%2Fshipanyai%2Fshipany-template-one)

### 3.2 手动部署步骤

#### 1. 准备代码仓库
```bash
git clone https://github.com/your-username/labubuwallpics.git
cd labubuwallpics
```

#### 2. 安装 Vercel CLI
```bash
npm install -g vercel
```

#### 3. 登录并部署
```bash
vercel login
vercel --prod
```

#### 4. 配置环境变量
在 Vercel Dashboard 中配置所有必要的环境变量。

### 3.3 自动部署配置

#### GitHub Actions 配置 (.github/workflows/deploy.yml)
```yaml
name: Deploy to Vercel
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Build project
        run: npm run build
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v20
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

## 4. Cloudflare Workers 部署

### 4.1 准备工作

#### 1. 安装依赖
```bash
npm install -g wrangler
```

#### 2. 登录 Cloudflare
```bash
wrangler login
```

#### 3. 配置 wrangler.toml
```toml
name = "labubuwallpics"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[vars]
NEXT_PUBLIC_WEB_URL = "https://your-domain.com"
SUPABASE_URL = "https://xxx.supabase.co"
# ... 其他非敏感环境变量

[[kv_namespaces]]
binding = "CACHE"
id = "your-kv-namespace-id"
```

#### 4. 配置敏感环境变量
```bash
# 设置敏感环境变量
wrangler secret put SUPABASE_SERVICE_ROLE_KEY
wrangler secret put STRIPE_PRIVATE_KEY
wrangler secret put OPENAI_API_KEY
# ... 其他敏感变量
```

### 4.2 部署命令

#### 构建和部署
```bash
# 构建项目
npm run cf:build

# 预览部署
npm run cf:preview

# 生产部署
npm run cf:deploy
```

#### 自定义域名配置
```bash
# 添加自定义域名
wrangler custom-domains add your-domain.com
```

## 5. Docker 部署

### 5.1 Dockerfile 配置
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json pnpm-lock.yaml* ./
RUN npm install -g pnpm && pnpm install --frozen-lockfile

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm install -g pnpm && pnpm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### 5.2 Docker Compose 配置
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env.production
    restart: unless-stopped
    
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped
```

### 5.3 部署命令
```bash
# 构建镜像
docker build -t labubuwallpics .

# 运行容器
docker run -p 3000:3000 --env-file .env.production labubuwallpics

# 使用 Docker Compose
docker-compose up -d
```

## 6. 数据库部署

### 6.1 Supabase 设置

#### 1. 创建项目
1. 访问 [Supabase Dashboard](https://supabase.com/dashboard)
2. 创建新项目
3. 记录项目 URL 和 API Keys

#### 2. 执行数据库迁移
```sql
-- 执行 data/install.sql 中的建表语句
-- 执行 data/wallpaper_install.sql 中的函数定义
```

#### 3. 配置 RLS (Row Level Security)
```sql
-- 启用 RLS
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE credits ENABLE ROW LEVEL SECURITY;

-- 创建策略
CREATE POLICY "Users can view own data" ON users
  FOR SELECT USING (auth.uid()::text = uuid);

CREATE POLICY "Users can update own data" ON users
  FOR UPDATE USING (auth.uid()::text = uuid);
```

### 6.2 数据备份策略

#### 自动备份配置
```bash
# 使用 Supabase CLI 进行备份
supabase db dump --file backup.sql

# 定时备份脚本
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
supabase db dump --file "backup_$DATE.sql"
```

## 7. 监控与日志

### 7.1 应用监控

#### Vercel Analytics
```javascript
// 在 app/layout.tsx 中添加
import { Analytics } from '@vercel/analytics/react';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <Analytics />
      </body>
    </html>
  );
}
```

#### 自定义监控
```javascript
// lib/monitoring.ts
export function trackEvent(event: string, properties?: any) {
  if (process.env.NODE_ENV === 'production') {
    // 发送到监控服务
    console.log('Event:', event, properties);
  }
}
```

### 7.2 错误监控

#### 全局错误处理
```javascript
// app/global-error.tsx
'use client';

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  // 发送错误到监控服务
  console.error('Global error:', error);
  
  return (
    <html>
      <body>
        <h2>Something went wrong!</h2>
        <button onClick={() => reset()}>Try again</button>
      </body>
    </html>
  );
}
```

### 7.3 性能监控

#### Web Vitals 监控
```javascript
// app/layout.tsx
import { SpeedInsights } from '@vercel/speed-insights/next';

export default function RootLayout({ children }) {
  return (
    <html>
      <body>
        {children}
        <SpeedInsights />
      </body>
    </html>
  );
}
```

## 8. 安全配置

### 8.1 HTTPS 配置

#### Vercel 自动 HTTPS
Vercel 自动为所有部署提供 HTTPS 证书。

#### Cloudflare SSL
1. 在 Cloudflare Dashboard 中启用 SSL
2. 选择 "Full (strict)" 模式

#### 自托管 SSL
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 8.2 安全头配置

#### next.config.mjs 安全配置
```javascript
/** @type {import('next').NextConfig} */
const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-Frame-Options',
            value: 'DENY',
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff',
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin',
          },
        ],
      },
    ];
  },
};

export default nextConfig;
```

## 9. 性能优化

### 9.1 缓存策略

#### 静态资源缓存
```javascript
// next.config.mjs
const nextConfig = {
  async headers() {
    return [
      {
        source: '/static/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable',
          },
        ],
      },
    ];
  },
};
```

#### API 响应缓存
```javascript
// app/api/example/route.ts
export async function GET() {
  const data = await fetchData();
  
  return Response.json(data, {
    headers: {
      'Cache-Control': 'public, max-age=300', // 5 分钟缓存
    },
  });
}
```

### 9.2 图片优化

#### Next.js Image 优化
```javascript
// next.config.mjs
const nextConfig = {
  images: {
    domains: ['your-r2-domain.com'],
    formats: ['image/webp', 'image/avif'],
  },
};
```

## 10. 故障排除

### 10.1 常见问题

#### 构建失败
```bash
# 清理缓存
rm -rf .next node_modules
npm install
npm run build
```

#### 环境变量问题
```bash
# 检查环境变量
echo $SUPABASE_URL
# 或在代码中
console.log('ENV:', process.env.SUPABASE_URL);
```

#### 数据库连接问题
```javascript
// 测试数据库连接
import { getSupabaseClient } from '@/models/db';

const supabase = getSupabaseClient();
const { data, error } = await supabase.from('users').select('count');
console.log('DB Test:', { data, error });
```

### 10.2 日志分析

#### 查看部署日志
```bash
# Vercel
vercel logs

# Cloudflare Workers
wrangler tail

# Docker
docker logs container-name
```

### 10.3 性能分析

#### 构建分析
```bash
# 分析包大小
npm run analyze

# 检查依赖
npm ls --depth=0
```

## 11. 维护计划

### 11.1 定期维护任务
- **每周**: 检查错误日志和性能指标
- **每月**: 更新依赖包和安全补丁
- **每季度**: 数据库性能优化和清理
- **每年**: 证书更新和架构评估

### 11.2 备份策略
- **数据库**: 每日自动备份
- **代码**: Git 版本控制
- **配置**: 环境变量备份
- **静态资源**: R2 存储备份

### 11.3 监控告警
- **服务可用性**: 99.9% SLA 监控
- **响应时间**: API 响应时间 < 2s
- **错误率**: 错误率 < 1%
- **资源使用**: CPU/内存使用率监控

---

**部署检查清单**:
- [ ] 环境变量配置完整
- [ ] 数据库连接正常
- [ ] 第三方服务集成测试
- [ ] HTTPS 证书配置
- [ ] 域名解析正确
- [ ] 监控和日志配置
- [ ] 备份策略实施
- [ ] 性能测试通过
