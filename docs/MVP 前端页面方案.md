### **壁纸列表页 (首页) 实现方案**

#### 1. 整体思路与架构

我们将采用 Next.js App Router 的最佳实践，以**服务器组件（Server Components）** 为主，仅在需要交互的地方使用**客户端组件（Client Components）**。

1.  **数据获取在服务端进行**：首页 `page.tsx` 将作为服务器组件，负责从我们刚刚创建的 `/api/wallpapers` API 获取壁纸数据。它会解析 URL 中的查询参数（如 `page`, `type`等）来请求相应的数据。
2.  **交互功能下沉到客户端组件**：
    *   **筛选栏 (`FilterBar`)** 将是一个客户端组件，因为它需要读取和修改 URL 的查询参数 (`searchParams`) 来触发筛选，并且不需要刷新整个页面。
    *   **分页组件 (`Pagination`)** 同样是客户端组件，用于在不丢失筛选条件的情况下切换页面。
    *   **壁纸卡片 (`WallpaperCard`)** 因为需要实现“鼠标悬停播放视频”的交互，也将是一个客户端组件。
3.  **组件化构建**：我们将创建一系列可复用的组件来构成整个页面，使代码结构清晰、易于维护。

#### 2. 文件结构规划

我将创建以下新组件，并对现有首页文件进行修改：

```
/components
└─ /blocks
   └─ /wallpaper
      ├─ FilterBar.tsx      # (新建) 筛选栏组件
      ├─ WallpaperGrid.tsx    # (新建) 壁纸网格布局组件
      ├─ WallpaperCard.tsx      # (新建) 单个壁纸卡片组件
      └─ Pagination.tsx       # (新建) 分页组件

/app
└─ /[locale]
   └─ /(default)
      └─ page.tsx           # (修改) 首页，集成新组件和数据获取逻辑
```

#### 3. 核心页面改造 (`app/[locale]/(default)/page.tsx`)

这是整个页面的入口和骨架。

1.  **转为服务器组件**：确保文件顶部没有 `"use client";`。
2.  **数据获取**：
    *   页面组件将接收 `searchParams` 作为 props，Next.js 会自动传入当前的 URL 查询参数。
    *   在组件内部，我们将调用我们之前创建的 `/api/wallpapers` 接口。我们会根据 `searchParams` 构建请求 URL，例如 `fetch('/api/wallpapers?page=2&type=static')`。
    *   这将获取到壁纸列表 `wallpapers` 和分页信息 `pagination`。
3.  **布局与组件集成**：
    *   我将分析现有 `page.tsx` 的布局，保留现有的所有组件，在此基础上集成新组件。
    *   在主内容区域，我将按顺序集成我们新创建的组件：
        *   `<FilterBar />`
        *   `<WallpaperGrid wallpapers={data.wallpapers} />`
        *   `<Pagination pagination={data.pagination} />`

#### 4. 组件详细设计

**A. `FilterBar.tsx`**

*   **定位**: 客户端交互组件，用于更新筛选条件。
*   **类型**: `"use client";`
*   **技术实现**:
    *   使用 `useSearchParams`, `useRouter`, `usePathname` from `next/navigation` 来读取和更新 URL。
    *   使用 `ToggleGroup` (from Shadcn UI) 来实现“类型”和“设备”的单选按钮组。
    *   当用户点击筛选按钮时，组件会生成新的查询字符串，并使用 `router.push()` 更新 URL，从而触发父页面 (服务器组件) 重新获取数据并渲染。

**B. `WallpaperGrid.tsx`**

*   **定位**: 服务端展示组件，负责渲染壁纸网格。
*   **类型**: Server Component
*   **技术实现**:
    *   接收 `wallpapers` 数组作为 prop。
    *   使用 Tailwind CSS 的 Grid 布局 (`grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4`) 来创建响应式网格。
    *   遍历 `wallpapers` 数组，为每个壁纸渲染一个 `<WallpaperCard />` 组件。
    *   如果 `wallpapers` 数组为空，则显示一个“无结果”的提示（可以使用 `components/blocks/empty` 组件）。

**C. `WallpaperCard.tsx`**

*   **定位**: 客户端交互组件，展示单个壁纸的预览。
*   **类型**: `"use client";`
*   **技术实现**:
    *   接收单个 `wallpaper` 对象作为 prop。
    *   使用 `Card` (from Shadcn UI) 作为基础容器。
    *   使用 `<Link href={'/wallpaper/' + wallpaper.id}>` 包裹整个卡片，使其可点击跳转。
    *   **图片/视频处理**:
        *   **静态壁纸**: 使用 `next/image` 显示 `static_image_thumbnail_url`。
        *   **动态壁纸**:
            *   默认显示海报图 (`live_poster_image_key` 对应的 URL)。
            *   在卡片一角使用 `Badge` (from Shadcn UI) 显示 "LIVE" 标签。
            *   使用 `useState` 和 `onMouseEnter`/`onMouseLeave` 事件来控制一个 `<video>` 元素的播放。鼠标悬停时，视频（静音、循环）淡入播放。
    *   在卡片底部显示壁纸 `title`。

**D. `Pagination.tsx`**

*   **定位**: 客户端交互组件，用于页面导航。
*   **类型**: `"use client";`
*   **技术实现**:
    *   接收 `pagination` 对象 (`{ current_page, total_pages }`) 作为 prop。
    *   使用 `Pagination` 组件族 (from Shadcn UI)。
    *   同样使用 `useSearchParams` 来获取当前的所有查询参数（如 `type` 和 `device`）。
    *   在生成“上一页”、“下一页”和页码链接时，会保留这些现有参数，仅修改 `page` 参数，确保筛选条件在翻页时不会丢失。

---

#### 5. 数据与交互流程总结

1.  用户访问首页。
2.  `page.tsx` (Server) 根据 URL `searchParams` 从 API 获取第一页数据。
3.  页面渲染出 `FilterBar`、`WallpaperGrid` 和 `Pagination`。
4.  **用户筛选**:
    *   用户在 `FilterBar` (Client) 上点击 "静态"。
    *   `FilterBar` 更新 URL 为 `/?type=static`。
    *   Next.js 自动重新渲染 `page.tsx` (Server)。
    *   `page.tsx` 带着新的 `searchParams` (`{ type: 'static' }`) 再次请求 API，获取筛选后的数据并重新渲染 `WallpaperGrid`。
5.  **用户翻页**:
    *   用户在 `Pagination` (Client) 点击 "第 2 页"。
    *   `Pagination` 更新 URL 为 `/?type=static&page=2`。
    *   流程同上，页面重新获取第二页的静态壁纸数据。