# Labubu 壁纸网站 - API 接口文档

## 1. API 概览

### 1.1 基础信息
- **Base URL**: `https://your-domain.com/api`
- **认证方式**: NextAuth Session 或 API Key
- **响应格式**: JSON
- **字符编码**: UTF-8

### 1.2 统一响应格式

#### 成功响应
```json
{
  "code": 0,
  "data": {
    // 具体数据
  },
  "message": "success"
}
```

#### 错误响应
```json
{
  "code": -1,
  "message": "错误描述",
  "data": null
}
```

#### 特殊状态码
- `code: -2` - 未认证 (需要登录)
- `code: -1` - 通用错误
- `code: 0` - 成功

## 2. 认证相关接口

### 2.1 获取用户信息
**接口**: `POST /api/get-user-info`

**描述**: 获取当前登录用户的详细信息

**请求头**:
```
Content-Type: application/json
Cookie: next-auth.session-token=xxx (自动携带)
```

**请求参数**: 无

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "id": 1,
    "uuid": "user-uuid-123",
    "email": "<EMAIL>",
    "nickname": "用户昵称",
    "avatar_url": "https://avatar.url",
    "created_at": "2024-01-01T00:00:00Z",
    "credits": {
      "left_credits": 100,
      "is_pro": true,
      "is_recharged": true
    }
  },
  "message": "success"
}
```

### 2.2 获取用户积分
**接口**: `POST /api/get-user-credits`

**描述**: 获取用户当前积分余额和状态

**请求参数**: 无

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "left_credits": 100,
    "is_pro": true,
    "is_recharged": true
  },
  "message": "success"
}
```

## 3. 支付相关接口

### 3.1 创建支付订单
**接口**: `POST /api/checkout`

**描述**: 创建 Stripe 支付会话

**请求参数**:
```json
{
  "credits": 100,
  "currency": "usd",
  "amount": 999,
  "interval": "one_time",
  "product_id": "credits_100",
  "product_name": "100 积分",
  "valid_months": 12,
  "cancel_url": "https://your-domain.com/cancel"
}
```

**参数说明**:
- `credits`: 购买的积分数量
- `currency`: 货币类型 (usd, cny)
- `amount`: 金额 (分为单位)
- `interval`: 支付类型 (one_time, month, year)
- `product_id`: 产品 ID
- `product_name`: 产品名称
- `valid_months`: 有效期 (月)
- `cancel_url`: 取消支付返回 URL

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "public_key": "pk_test_xxx",
    "order_no": "order_123456",
    "session_id": "cs_test_xxx"
  },
  "message": "success"
}
```

### 3.2 支付成功处理
**接口**: `GET /[locale]/pay-success/[session_id]`

**描述**: 支付成功后的回调处理 (页面路由，非 API)

**参数**:
- `session_id`: Stripe 会话 ID

**处理流程**:
1. 验证 Stripe 支付状态
2. 更新订单状态为已支付
3. 发放对应积分
4. 重定向到成功页面

## 4. AI 功能接口

### 4.1 文本生成
**接口**: `POST /api/demo/gen-text`

**描述**: 使用 AI 生成文本内容

**请求参数**:
```json
{
  "prompt": "请写一首关于春天的诗",
  "provider": "openai",
  "model": "gpt-4"
}
```

**支持的提供商**:
- `openai`: OpenAI GPT 系列
- `deepseek`: DeepSeek 模型
- `openrouter`: OpenRouter 平台
- `siliconflow`: SiliconFlow 平台

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "text": "生成的文本内容...",
    "reasoning": "推理过程 (仅支持特定模型)"
  },
  "message": "success"
}
```

### 4.2 流式文本生成
**接口**: `POST /api/demo/gen-stream-text`

**描述**: 流式生成文本内容

**请求参数**: 同文本生成接口

**响应格式**: Server-Sent Events (SSE) 流

### 4.3 图像生成
**接口**: `POST /api/demo/gen-image`

**描述**: 使用 AI 生成图像

**请求参数**:
```json
{
  "prompt": "一只可爱的猫咪",
  "provider": "openai",
  "model": "dall-e-3"
}
```

**支持的提供商**:
- `openai`: DALL-E 系列
- `replicate`: Replicate 平台模型
- `kling`: Kling AI 图像生成

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "images": [
      {
        "url": "https://r2.domain.com/generated-image.jpg",
        "base64": "data:image/jpeg;base64,..."
      }
    ]
  },
  "message": "success"
}
```

### 4.4 视频生成
**接口**: `POST /api/demo/gen-video`

**描述**: 使用 AI 生成视频内容

**请求参数**:
```json
{
  "prompt": "一只猫在花园里玩耍",
  "provider": "kling",
  "model": "kling-v1",
  "duration": 5,
  "aspect_ratio": "16:9"
}
```

**参数说明**:
- `duration`: 视频时长 (5 或 10 秒)
- `aspect_ratio`: 宽高比 (16:9, 9:16, 1:1)

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "videos": [
      {
        "url": "https://r2.domain.com/generated-video.mp4",
        "base64": "data:video/mp4;base64,..."
      }
    ]
  },
  "message": "success"
}
```

## 5. 测试接口

### 5.1 Ping 测试
**接口**: `POST /api/ping`

**描述**: 测试接口连通性，消耗 1 积分

**请求参数**:
```json
{
  "message": "hello"
}
```

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "pong": "received message: hello"
  },
  "message": "success"
}
```

## 6. 反馈接口

### 6.1 提交反馈
**接口**: `POST /api/add-feedback`

**描述**: 用户提交反馈意见

**请求参数**:
```json
{
  "content": "反馈内容",
  "rating": 5
}
```

**参数说明**:
- `content`: 反馈内容 (必填)
- `rating`: 评分 1-5 (可选)

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "user_uuid": "user-uuid-123",
    "content": "反馈内容",
    "rating": 5,
    "created_at": "2024-01-01T00:00:00Z",
    "status": "created"
  },
  "message": "success"
}
```

## 7. 错误码说明

### 7.1 通用错误码
- `code: 0` - 成功
- `code: -1` - 通用错误
- `code: -2` - 未认证，需要登录

### 7.2 常见错误信息
- `"invalid params"` - 请求参数无效
- `"no auth"` - 未认证或认证失败
- `"insufficient credits"` - 积分不足
- `"user not exist"` - 用户不存在
- `"operation failed"` - 操作失败

## 8. 认证方式

### 8.1 Session 认证 (推荐)
通过 NextAuth.js 管理的会话认证，适用于 Web 前端：

```javascript
// 前端调用示例
const response = await fetch('/api/get-user-info', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  credentials: 'include', // 自动携带 Cookie
});
```

### 8.2 API Key 认证
适用于服务端调用或第三方集成：

```javascript
// API Key 调用示例
const response = await fetch('/api/ping', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'X-API-Key': 'your-api-key-here',
  },
  body: JSON.stringify({
    message: 'hello'
  }),
});
```

## 9. 限流和配额

### 9.1 积分消耗
不同 API 调用消耗的积分数量：
- `ping`: 1 积分
- `gen-text`: 根据模型和长度计算
- `gen-image`: 根据模型和数量计算
- `gen-video`: 根据时长和质量计算

### 9.2 调用限制
- 单次请求超时: 30 秒
- 并发请求限制: 10 个/用户
- 文件上传大小限制: 10MB

## 10. SDK 和示例

### 10.1 JavaScript SDK 示例
```javascript
class LabububAPI {
  constructor(apiKey = null) {
    this.baseURL = 'https://your-domain.com/api';
    this.apiKey = apiKey;
  }

  async request(endpoint, data) {
    const headers = {
      'Content-Type': 'application/json',
    };
    
    if (this.apiKey) {
      headers['X-API-Key'] = this.apiKey;
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, {
      method: 'POST',
      headers,
      body: JSON.stringify(data),
      credentials: this.apiKey ? 'omit' : 'include',
    });

    return await response.json();
  }

  async generateText(prompt, provider = 'openai', model = 'gpt-4') {
    return await this.request('/demo/gen-text', {
      prompt,
      provider,
      model,
    });
  }

  async generateImage(prompt, provider = 'openai', model = 'dall-e-3') {
    return await this.request('/demo/gen-image', {
      prompt,
      provider,
      model,
    });
  }
}

// 使用示例
const api = new LabububAPI('your-api-key');
const result = await api.generateText('写一首诗');
console.log(result.data.text);
```

### 10.2 Python SDK 示例
```python
import requests
import json

class LabububAPI:
    def __init__(self, api_key=None):
        self.base_url = 'https://your-domain.com/api'
        self.api_key = api_key
    
    def request(self, endpoint, data):
        headers = {'Content-Type': 'application/json'}
        if self.api_key:
            headers['X-API-Key'] = self.api_key
        
        response = requests.post(
            f'{self.base_url}{endpoint}',
            headers=headers,
            json=data
        )
        return response.json()
    
    def generate_text(self, prompt, provider='openai', model='gpt-4'):
        return self.request('/demo/gen-text', {
            'prompt': prompt,
            'provider': provider,
            'model': model
        })

# 使用示例
api = LabububAPI('your-api-key')
result = api.generate_text('写一首诗')
print(result['data']['text'])
```

## 11. 更新日志

### v2.0.0 (当前版本)
- 支持多 AI 提供商集成
- 新增视频生成功能
- 优化积分系统
- 完善错误处理

### v1.0.0
- 基础用户系统
- 支付集成
- 基础 AI 功能

---

**注意事项**:
1. 所有 API 调用都需要适当的认证
2. 请合理使用积分，避免不必要的浪费
3. 大文件上传建议使用分片上传
4. 生产环境请使用 HTTPS
5. 建议实现客户端重试机制处理网络异常
