### **壁纸详情页实现方案**

#### 1. 整体思路与架构

我们将继续遵循 Next.js App Router 的最佳实践，构建一个清晰、高效的详情页。

1.  **服务端数据获取**: 详情页 `app/[locale]/(default)/wallpaper/[id]/page.tsx` 将作为**服务器组件**。它会利用 `params.id` 从我们之前创建的 `/api/wallpapers/[id]` 接口获取单个壁纸的详细数据。
2.  **客户端组件处理交互**:
    *   **视频播放器 (`VideoPlayer`)**: 对于动态壁纸，我们需要一个客户端组件来处理视频的播放、暂停、音量控制等交互功能。
    *   **下载按钮 (`DownloadButton`)**: 虽然下载功能本身很简单（一个带 `download` 属性的 `<a>` 标签），但如果未来需要增加点击跟踪或复杂逻辑，可以将其封装成一个客户端组件。MVP阶段，可以先用简单的 `<a>` 标签。
3.  **清晰的组件化**: 页面将被拆分为几个逻辑组件，如预览区、信息区等，便于管理和复用。

#### 2. 文件结构规划

```
/components
└─ /blocks
   └─ /wallpaper-detail
      ├─ WallpaperPreview.tsx   # (新建) 预览区 (图片或视频)
      ├─ WallpaperInfo.tsx      # (新建) 信息与操作区
      └─ VideoPlayer.tsx        # (新建) 视频播放器客户端组件

/app
└─ /[locale]
   └─ /(default)
      └─ /wallpaper
         └─ /[id]
            └─ page.tsx         # (新建) 详情页主文件
```

#### 3. 核心页面实现 (`app/[locale]/(default)/wallpaper/[id]/page.tsx`)

1.  **数据获取**:
    *   页面组件将接收 `params` 作为 props，从中获取 `id`。
    *   调用 `/api/wallpapers/[id]` 接口获取壁纸数据。
    *   如果 API 返回 404 或错误，我们将使用 Next.js 的 `notFound()` 函数来渲染一个标准的 404 页面。
2.  **页面布局**:
    *   我将使用一个响应式的两栏布局。在桌面端，左侧是预览区，右侧是信息区；在移动端，两栏将垂直堆叠。
    *   集成新创建的组件：
        ```jsx
        <div className="grid md:grid-cols-2 gap-8">
          <WallpaperPreview wallpaper={wallpaper} />
          <WallpaperInfo wallpaper={wallpaper} />
        </div>
        ```

#### 4. 组件详细设计

**A. `WallpaperPreview.tsx` (Server Component)**

*   **功能**: 根据壁纸类型，渲染静态图片预览或动态视频播放器。
*   **技术实现**:
    *   接收 `wallpaper` 对象作为 prop。
    *   **条件渲染**:
        *   如果 `wallpaper.wallpaper_type === 'static'`，则使用 `next/image` 显示高清预览图 (`static_image_preview_url`)。
        *   如果 `wallpaper.wallpaper_type === 'live'`，则渲染 `<VideoPlayer />` 组件，并传入视频源 URL (`live_video_url` - 假设该字段存在)。

**B. `VideoPlayer.tsx` (Client Component)**

*   **功能**: 提供带完整控件的视频播放体验。
*   **类型**: `"use client";`
*   **技术实现**:
    *   接收视频 `src` 和海报 `poster` URL 作为 props。
    *   使用原生的 `<video>` 标签，并开启 `controls` 属性，浏览器将提供默认的播放控件（播放/暂停、进度条、音量、全屏）。这能最快地满足 MVP 需求。
    *   设置 `autoPlay` 和 `muted` 属性，实现静音自动播放。

**C. `WallpaperInfo.tsx` (Server Component)**

*   **功能**: 展示壁纸的所有元数据和操作按钮。
*   **技术实现**:
    *   接收 `wallpaper` 对象作为 prop。
    *   **信息展示**:
        *   使用 `<h1>` 显示醒目的 `title`。
        *   使用 `Badge` (from Shadcn UI) 或简单的文本来展示属性，如类型 (`wallpaper_type`)、适配设备 (`device_target`)、分辨率 (`width`x`height`)等。
        *   每个属性前可以加上一个合适的图标（如 `Monitor`, `Smartphone` from `lucide-react`）。
    *   **下载按钮**:
        *   使用 `Button` (from Shadcn UI) 组件创建一个醒目的下载按钮。
        *   用一个 `<a>` 标签包裹它，`href` 指向原始文件 URL (`static_image_original_url` 或 `live_video_original_url`)。
        *   添加 `download` 属性，以确保点击时是下载文件而不是在浏览器中打开。
        *   按钮文本根据壁纸类型动态显示，例如 "Download HD Wallpaper" 或 "Download MP4 Video"。

---

这个方案旨在快速、高效地实现一个功能完整且体验良好的壁纸详情页，同时保持代码的整洁和未来的可扩展性。 