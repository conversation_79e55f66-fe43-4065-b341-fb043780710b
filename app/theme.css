:root {
  --background: oklch(0.9636 0.0328 106.9994);
  --foreground: oklch(0.3211 0 0);
  --card: oklch(1.0000 0 0);
  --card-foreground: oklch(0 0 0);
  --popover: oklch(0.9551 0 0);
  --popover-foreground: oklch(0 0 0);
  --primary: oklch(0.6138 0.1129 122.7118);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.8920 0.0453 146.1898);
  --secondary-foreground: oklch(0 0 0);
  --muted: oklch(0.9233 0.0067 286.2674);
  --muted-foreground: oklch(0 0 0);
  --accent: oklch(0.5782 0.1372 144.2082);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.6520 0.2340 26.6909);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.8975 0 0);
  --input: oklch(0.9761 0 0);
  --ring: oklch(0.7380 0 0);
  --chart-1: oklch(0.6138 0.1129 122.7118);
  --chart-2: oklch(0.8920 0.0453 146.1898);
  --chart-3: oklch(0.9636 0.0328 106.9994);
  --chart-4: oklch(0.9233 0.0067 286.2674);
  --chart-5: oklch(0.5782 0.1372 144.2082);
  --sidebar: oklch(0.9551 0 0);
  --sidebar-foreground: oklch(0 0 0);
  --sidebar-primary: oklch(0.6138 0.1129 122.7118);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.5782 0.1372 144.2082);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.8975 0 0);
  --sidebar-ring: oklch(0.7380 0 0);
  --font-sans: Inter;
  --font-serif: Merriweather;
  --font-mono: JetBrains Mono;
  --radius: 8px;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.10);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.10);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.20), 0px 1px 2px -1px hsl(0 0% 0% / 0.20);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.20), 0px 1px 2px -1px hsl(0 0% 0% / 0.20);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.20), 0px 2px 4px -1px hsl(0 0% 0% / 0.20);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.20), 0px 4px 6px -1px hsl(0 0% 0% / 0.20);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.20), 0px 8px 10px -1px hsl(0 0% 0% / 0.20);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.50);
  --tracking-normal: 0.2px;
}

.dark {
  --background: oklch(0.3052 0 0);
  --foreground: oklch(1.0000 0 0);
  --card: oklch(0.3211 0 0);
  --card-foreground: oklch(1.0000 0 0);
  --popover: oklch(0.3867 0 0);
  --popover-foreground: oklch(1.0000 0 0);
  --primary: oklch(0.5565 0.1323 273.7949);
  --primary-foreground: oklch(1.0000 0 0);
  --secondary: oklch(0.6138 0.1129 122.7118);
  --secondary-foreground: oklch(1.0000 0 0);
  --muted: oklch(0.4495 0 0);
  --muted-foreground: oklch(1.0000 0 0);
  --accent: oklch(0.5782 0.1372 144.2082);
  --accent-foreground: oklch(1.0000 0 0);
  --destructive: oklch(0.6520 0.2340 26.6909);
  --destructive-foreground: oklch(1.0000 0 0);
  --border: oklch(0.3867 0 0);
  --input: oklch(0.3211 0 0);
  --ring: oklch(0.4495 0 0);
  --chart-1: oklch(0.5565 0.1323 273.7949);
  --chart-2: oklch(0.6138 0.1129 122.7118);
  --chart-3: oklch(0.3052 0 0);
  --chart-4: oklch(0.4495 0 0);
  --chart-5: oklch(0.5782 0.1372 144.2082);
  --sidebar: oklch(0.3211 0 0);
  --sidebar-foreground: oklch(1.0000 0 0);
  --sidebar-primary: oklch(0.5565 0.1323 273.7949);
  --sidebar-primary-foreground: oklch(1.0000 0 0);
  --sidebar-accent: oklch(0.5782 0.1372 144.2082);
  --sidebar-accent-foreground: oklch(1.0000 0 0);
  --sidebar-border: oklch(0.3867 0 0);
  --sidebar-ring: oklch(0.4495 0 0);
  --font-sans: Inter;
  --font-serif: Merriweather;
  --font-mono: JetBrains Mono;
  --radius: 8px;
  --shadow-2xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.20);
  --shadow-xs: 0px 2px 4px 0px hsl(0 0% 0% / 0.20);
  --shadow-sm: 0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 1px 2px -1px hsl(0 0% 0% / 0.40);
  --shadow: 0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 1px 2px -1px hsl(0 0% 0% / 0.40);
  --shadow-md: 0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 2px 4px -1px hsl(0 0% 0% / 0.40);
  --shadow-lg: 0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 4px 6px -1px hsl(0 0% 0% / 0.40);
  --shadow-xl: 0px 2px 4px 0px hsl(0 0% 0% / 0.40), 0px 8px 10px -1px hsl(0 0% 0% / 0.40);
  --shadow-2xl: 0px 2px 4px 0px hsl(0 0% 0% / 1.00);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);

  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-normal: var(--tracking-normal);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
}

body {
  letter-spacing: var(--tracking-normal);
}