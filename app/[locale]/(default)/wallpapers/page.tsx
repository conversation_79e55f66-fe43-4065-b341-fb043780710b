import { getTranslations, setRequestLocale } from "next-intl/server";
import { FilterBar } from "@/components/blocks/wallpaper/FilterBar";
import { WallpaperGrid } from "@/components/blocks/wallpaper/WallpaperGrid";
import { WallpaperPagination } from "@/components/blocks/wallpaper/Pagination";
import { <PERSON>, <PERSON>rk<PERSON>, Star } from "lucide-react";

export const revalidate = 60;
export const dynamicParams = true;

interface WallpapersPageProps {
  params: Promise<{
    locale: string;
  }>;
  searchParams: Promise<{
    [key: string]: string | string[] | undefined;
  }>;
}

// 获取壁纸数据的函数
async function getWallpapers(searchParams: { [key: string]: string | string[] | undefined }) {
  const params = new URLSearchParams();
  
  // 处理 sort 参数，将其转换为 API 期望的格式
  const sort = searchParams.sort as string;
  if (sort === 'popular') {
    params.append('sortBy', 'download_count');
    params.append('order', 'desc');
  } else if (sort === 'newest') {
    params.append('sortBy', 'created_at');
    params.append('order', 'desc');
  }
  
  // 处理其他查询参数
  for (const key in searchParams) {
    if (Object.prototype.hasOwnProperty.call(searchParams, key) && key !== 'sort') {
      const value = searchParams[key];
      if (value) {
        params.append(key, Array.isArray(value) ? value.join(',') : value);
      }
    }
  }

  const webUrl = process.env.NEXT_PUBLIC_WEB_URL || '';
  const apiUrl = `${webUrl}/api/wallpapers?${params.toString()}`;
  
  try {
    const res = await fetch(apiUrl, { next: { revalidate: 30 } });
    if (!res.ok) {
      throw new Error(`Failed to fetch wallpapers: ${res.statusText}`);
    }
    const data = await res.json();
    return data.data; // API 响应包装在 "data" 对象中
  } catch (error) {
    console.error("Error fetching wallpapers:", error);
    return { wallpapers: [], pagination: { current_page: 1, total_pages: 0 } };
  }
}

export default async function WallpapersPage({ params, searchParams }: WallpapersPageProps) {
  const t = await getTranslations('LandingPage');
  const { locale } = await params;
  const resolvedSearchParams = await searchParams;
  setRequestLocale(locale);

  // 获取壁纸数据
  const { wallpapers, pagination } = await getWallpapers(resolvedSearchParams);
  
  // 根据 sort 参数确定页面标题
  const sort = resolvedSearchParams.sort as string;
  const pageTitle = sort === 'popular' ? t('popular_wallpapers') : sort === 'newest' ? t('latest_wallpapers') : t('all_wallpapers');

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2 text-primary flex items-center gap-3">
          {sort === 'popular' && <Flame className="size-8 text-primary" />}
          {sort === 'newest' && <Sparkles className="size-8 text-primary" />}
          {!sort && <Star className="size-8 text-primary" />}
          {pageTitle}
        </h1>
        <p className="text-primary">
          {sort === 'popular' && t('popular_wallpapers_description')}
          {sort === 'newest' && t('latest_wallpapers_description')}
          {!sort && t('all_wallpapers_description')}
        </p>
      </div>

      {/* 筛选栏 */}
      <div className="mb-10">
        <FilterBar />
      </div>

      {/* 壁纸网格 */}
      <WallpaperGrid wallpapers={wallpapers} />

      {/* 分页 */}
      <div className="mt-12 flex justify-center">
        <WallpaperPagination pagination={pagination} />
      </div>
    </div>
  );
} 