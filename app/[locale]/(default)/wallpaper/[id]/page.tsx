import { notFound } from "next/navigation";
import { setRequestLocale } from "next-intl/server";

import { WallpaperPreview } from "@/components/blocks/wallpaper-detail/WallpaperPreview";
import { WallpaperInfo } from "@/components/blocks/wallpaper-detail/WallpaperInfo";

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string, id: string }>;
}) {
  const { locale, id } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/wallpaper/${id}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}/wallpaper/${id}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

// We will create these components in the next steps
// import { WallpaperPreview } from "@/components/blocks/wallpaper-detail/WallpaperPreview";
// import { WallpaperInfo } from "@/components/blocks/wallpaper-detail/WallpaperInfo";

interface WallpaperPageProps {
  params: Promise<{
    locale: string;
    id: string;
  }>;
}

// Function to fetch a single wallpaper from the API
async function getWallpaper(id: string) {
    const webUrl = process.env.NEXT_PUBLIC_WEB_URL || '';
    const apiUrl = `${webUrl}/api/wallpapers/${id}`;

    try {
        const res = await fetch(apiUrl, { next: { revalidate: 3600 } }); // Revalidate every hour
        // const res = await fetch(apiUrl, { cache: 'no-store' }); // Disable cache for debugging
        if (!res.ok) {
            if (res.status === 404) {
                return null; // Handle not found case
            }
            throw new Error(`Failed to fetch wallpaper: ${res.statusText}`);
        }
        const data = await res.json();
        return data.data; // Assuming the API response is wrapped in a "data" object
    } catch (error) {
        console.error(`Error fetching wallpaper with id ${id}:`, error);
        return null;
    }
}


export default async function WallpaperPage({ params }: WallpaperPageProps) {
  const { locale, id } = await params;

  setRequestLocale(locale);

  const wallpaper = await getWallpaper(id);

  if (!wallpaper) {
    notFound();
  }

  return (
    <div className="container mx-auto px-4 py-12 sm:py-16">
        <div className="grid md:grid-cols-5 gap-8 lg:gap-16 items-start">
            <div className="md:col-span-3">
                <WallpaperPreview wallpaper={wallpaper} />
            </div>
            <div className="md:col-span-2">
                <WallpaperInfo wallpaper={wallpaper} />
            </div>
        </div>
    </div>
  );
} 