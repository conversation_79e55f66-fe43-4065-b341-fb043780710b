import Branding from "@/components/blocks/branding";
import CTA from "@/components/blocks/cta";
import FAQ from "@/components/blocks/faq";
import Feature from "@/components/blocks/feature";
import Feature1 from "@/components/blocks/feature1";
import Feature2 from "@/components/blocks/feature2";
import Feature3 from "@/components/blocks/feature3";
import Hero from "@/components/blocks/hero";
import Pricing from "@/components/blocks/pricing";
import Showcase from "@/components/blocks/showcase";
import Stats from "@/components/blocks/stats";
import Testimonial from "@/components/blocks/testimonial";
import { getLandingPage } from "@/services/page";
import { getTranslations, setRequestLocale } from "next-intl/server";

// 新的首页组件
import { HomepageHero } from "@/components/blocks/hero/HomepageHero";
import { WallpaperGridSection } from "@/components/blocks/wallpaper/WallpaperGridSection";
import { WallpaperStats } from "@/components/blocks/wallpaper-stats";
import { <PERSON>, <PERSON>rk<PERSON>, <PERSON> } from "lucide-react";

export const revalidate = 60;
export const dynamicParams = true;

export async function generateMetadata({
  params,
}: {
  params: Promise<{ locale: string }>;
}) {
  const { locale } = await params;
  let canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}`;

  if (locale !== "en") {
    canonicalUrl = `${process.env.NEXT_PUBLIC_WEB_URL}/${locale}`;
  }

  return {
    alternates: {
      canonical: canonicalUrl,
    },
  };
}

interface LandingPageProps {
  params: Promise<{
    locale: string;
  }>;
}

// 获取壁纸数据的函数
async function getWallpapersData() {
  const webUrl = process.env.NEXT_PUBLIC_WEB_URL || '';
  
  try {
    // 并行获取热门和最新壁纸
    const [popularResponse, newResponse, allResponse] = await Promise.all([
      fetch(`${webUrl}/api/wallpapers?sortBy=download_count&order=desc&limit=8`, { 
        next: { revalidate: 300 } // 5分钟缓存
      }),
      fetch(`${webUrl}/api/wallpapers?sortBy=created_at&order=desc&limit=8`, { 
        next: { revalidate: 60 } // 1分钟缓存，新内容更新更频繁
      }),
      fetch(`${webUrl}/api/wallpapers?device=mobile&sortBy=download_count&order=desc`, { 
        next: { revalidate: 86400 } // 1天缓存
      })
    ]);

    if (!popularResponse.ok || !newResponse.ok || !allResponse.ok) {
      throw new Error('Failed to fetch wallpapers');
    }

    const [popularData, newData, allData] = await Promise.all([
      popularResponse.json(),
      newResponse.json(),
      allResponse.json()
    ]);

    return {
      popularWallpapers: popularData.data?.wallpapers || [],
      newWallpapers: newData.data?.wallpapers || [],
      allWallpapers: allData.data?.wallpapers || []
    };
  } catch (error) {
    console.error("Error fetching wallpapers:", error);
    return {
      popularWallpapers: [],
      newWallpapers: [],
      allWallpapers: []
    };
  }
}

export default async function LandingPage({ params }: LandingPageProps) {
  const { locale } = await params;
  setRequestLocale(locale);
  const t = await getTranslations('LandingPage');

  // 并行获取页面数据和壁纸数据
  const [page, { popularWallpapers, newWallpapers, allWallpapers }] = await Promise.all([
    getLandingPage(locale),
    getWallpapersData()
  ]);

  // 焦点轮播区的数据从最新壁纸中选取前3个
  // const heroWallpapers = allWallpapers.slice(0, 10);

  const tempWallpapers = [...allWallpapers];
  const heroWallpapers = [];
  // 确保要选取的数量不超过数组总长度
  const numToPick = Math.min(10, tempWallpapers.length);

  for (let i = 0; i < numToPick; i++) {
    // 从临时数组中随机选取一个索引
    const randomIndex = Math.floor(Math.random() * tempWallpapers.length);
    
    // 使用 splice 从临时数组中“取出”该元素，并添加到结果数组中
    // splice 会返回一个包含已删除元素的数组，所以我们取 [0]
    heroWallpapers.push(tempWallpapers.splice(randomIndex, 1)[0]);
}

  return (
    <>
      {/* 1. 恢复原 Hero 组件以保留其完整样式, 并使用更小的间距 */}
      {page.hero && <Hero hero={page.hero} size="small" />}
      
      {/* 2. 将壁纸统计区域紧随其后 */}
      {/* <WallpaperStats /> */}

      {/* 3. 创建新的"精选壁纸"轮播区域 */}
      <section className="container mx-auto px-4 mb-16 -mt-4 sm:-mt-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-2xl md:text-3xl font-bold text-primary flex items-center gap-3">
            <Star className="size-8 text-primary" />
            {t('featured_wallpapers')}
          </h2>
        </div>
        <HomepageHero wallpapers={heroWallpapers} />
      </section>

      {/* 2. 将壁纸统计区域紧随其后 */}
      <WallpaperStats />

      {/* 4. 保留热门下载区 */}
      <WallpaperGridSection 
        title={t('popular_wallpapers')} 
        wallpapers={popularWallpapers} 
        viewAllHref="/wallpapers?sort=popular"
        icon={Flame}
      />

      {/* 5. 保留最新上架区 */}
      <WallpaperGridSection 
        title={t('latest_wallpapers')} 
        wallpapers={newWallpapers} 
        viewAllHref="/wallpapers?sort=newest"
        icon={Sparkles}
      />

      {/* 保留的其他区块 */}
      {page.feature && <Feature section={page.feature} />}
      {page.showcase && <Showcase section={page.showcase} />}
      {page.pricing && <Pricing pricing={page.pricing} />}
      {page.testimonial && <Testimonial section={page.testimonial} />}
      {page.faq && <FAQ section={page.faq} />}
    </>
  );
}
