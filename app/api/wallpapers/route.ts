import { NextRequest } from "next/server";
import { getSupabaseClient } from "@/models/db";
import { respData, respErr } from "@/lib/resp";

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = req.nextUrl;

    // 1. Parse and validate query parameters
    const page = parseInt(searchParams.get("page") || "1", 10);
    const limit = parseInt(searchParams.get("limit") || "20", 10);
    const type = searchParams.get("type");
    const device = searchParams.get("device");
    const sortBy = searchParams.get("sortBy") || "download_count";
    const order = searchParams.get("order") || "desc";
    const q = searchParams.get("q");

    if (page < 1 || limit < 1 || limit > 100) {
      return respErr("Invalid pagination parameters");
    }

    const from = (page - 1) * limit;
    const to = from + limit - 1;

    const supabase = getSupabaseClient();
    
    // 2. Build database query dynamically
    let query = supabase
      .from("labubu_wallpapers")
      .select("*", { count: "exact" })
      .eq("is_published", true); // Always filter for published wallpapers

    // --- Start of Debugging & Logic Fix ---
    const appliedFilters: { [key: string]: any } = { is_published: true };

    if (type) {
      query = query.eq("wallpaper_type", type);
      appliedFilters.wallpaper_type = type;
    }
    if (device) {
      query = query.eq("device_target", device);
      appliedFilters.device_target = device;
    }
    if (q) {
      query = query.ilike("title", `%${q}%`);
      appliedFilters.title = `like *${q}*`;
    }

    query = query.order(sortBy, { ascending: order === "asc" })
              .order("created_at", { ascending: false })
              .range(from, to);

    // Print the constructed query details to the console for debugging
    console.log("--- Supabase Query ---");
    console.log("Applied Filters:", appliedFilters);
    console.log("Sorting:", { by: sortBy, direction: order });
    console.log("Pagination:", { page, limit, from, to });
    console.log("----------------------");
    // --- End of Debugging & Logic Fix ---

    // 3. Execute database query
    const { data, error, count } = await query;

    if (error) {
      console.error("Supabase query error:", error);
      return respErr("Failed to fetch wallpapers from database");
    }

    // 4. Process results to generate full URLs
    const r2Domain = process.env.STORAGE_DOMAIN;
    if (!r2Domain) {
        console.error("R2 domain is not configured");
        return respErr("Server configuration error: R2 domain is missing");
    }

    const wallpapersWithUrls = data.map((wallpaper) => {
        const fullUrl = (key: string | null) => key ? `https://${r2Domain}/${key}` : null;
        return {
            ...wallpaper,
            // Static wallpaper URLs
            static_image_original_url: fullUrl(wallpaper.static_image_original_key),
            static_image_preview_url: fullUrl(wallpaper.static_image_preview_key),
            static_image_thumbnail_url: fullUrl(wallpaper.static_image_thumbnail_key),
            // Live wallpaper URLs
            live_video_url: fullUrl(wallpaper.live_video_key),
            live_poster_image_url: fullUrl(wallpaper.live_poster_image_key),
            live_thumbnail_video_url: fullUrl(wallpaper.live_thumbnail_video_key),
        };
    });

    // 5. Format and return response
    const totalPages = count ? Math.ceil(count / limit) : 0;
    
    return respData({
      wallpapers: wallpapersWithUrls,
      pagination: {
        current_page: page,
        total_pages: totalPages,
        total_items: count,
        limit,
      },
    });

  } catch (error) {
    console.error("Get wallpapers failed:", error);
    if (error instanceof Error) {
      return respErr(error.message);
    }
    return respErr("An unknown error occurred while fetching wallpapers");
  }
}
