import { NextRequest, NextResponse } from "next/server";
import { getSupabaseClient } from "@/models/db";
import { respData, respErr } from "@/lib/resp";

export async function GET(
  req: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await context.params;

    if (!id) {
      return new NextResponse(JSON.stringify(respErr("Missing wallpaper ID")), { status: 400 });
    }

    const supabase = getSupabaseClient();
    
    // Build and execute the query
    const { data: wallpaper, error } = await supabase
      .from("labubu_wallpapers")
      .select("*")
      .eq("id", id)
      .eq("is_published", true)
      .single();

    if (error) {
        if (error.code === 'PGRST116') { // PostgREST code for "No rows found"
            return new NextResponse(JSON.stringify(respErr("Wallpaper not found")), { status: 404 });
        }
        console.error("Supabase query error:", error);
        return respErr("Failed to fetch wallpaper from database");
    }

    if (!wallpaper) {
        return new NextResponse(JSON.stringify(respErr("Wallpaper not found")), { status: 404 });
    }

    // Process result to generate full URLs
    const r2Domain = process.env.STORAGE_DOMAIN;
    if (!r2Domain) {
        console.error("R2 domain is not configured");
        return respErr("Server configuration error: R2 domain is missing");
    }

    const fullUrl = (key: string | null) => key ? `https://${r2Domain}/${key}` : null;
    const wallpaperWithUrls = {
        ...wallpaper,
        static_image_original_url: fullUrl(wallpaper.static_image_original_key),
        static_image_preview_url: fullUrl(wallpaper.static_image_preview_key),
        static_image_thumbnail_url: fullUrl(wallpaper.static_image_thumbnail_key),
        // Live wallpaper URLs
        live_video_original_url: fullUrl(wallpaper.live_video_key),
        live_video_url: fullUrl(wallpaper.live_video_key),
        live_poster_image_url: fullUrl(wallpaper.live_poster_image_key),
        live_thumbnail_video_url: fullUrl(wallpaper.live_thumbnail_video_key),
    };

    return respData(wallpaperWithUrls);

  } catch (error) {
    console.error("Get wallpaper detail failed:", error);
    if (error instanceof Error) {
      return respErr(error.message);
    }
    return respErr("An unknown error occurred while fetching wallpaper detail");
  }
} 