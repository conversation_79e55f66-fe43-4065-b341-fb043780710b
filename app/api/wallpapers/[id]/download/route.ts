import { getSupabaseClient } from "@/models/db";
import { NextResponse } from 'next/server';
import { PostgrestError } from "@supabase/supabase-js";

export async function GET(
  request: Request,
  context: { params: Promise<{ id: string }> }
) {
  const { id } = await context.params;
  const wallpaperId = id;
  if (!wallpaperId) {
    return new NextResponse('Wallpaper ID is required', { status: 400 });
  }

  const supabase = getSupabaseClient();

  // The sole responsibility of this endpoint is to increment the download count.
  const { error } = await supabase.rpc('increment_download_count', {
    wallpaper_id: wallpaperId,
  });

  if (error) {
    // Log the error for debugging, but don't block the user or return an error response.
    // The download should proceed on the client-side regardless.
    console.error('Failed to increment download count for wallpaper ID:', wallpaperId, error);
  }

  // Return a 204 No Content response to indicate success without a body.
  return new NextResponse(null, { status: 204 });
} 