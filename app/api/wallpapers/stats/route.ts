import { getSupabaseClient } from "@/models/db";
import { respData, respErr } from "@/lib/resp";

export const revalidate = 60; // Revalidate every 60 seconds

export async function GET() {
  try {
    const supabase = getSupabaseClient();

    // Fetch all counts in parallel for efficiency
    const [
      { count: totalCount },
      { count: liveCount },
      { count: staticCount }
    ] = await Promise.all([
      supabase
        .from("labubu_wallpapers")
        .select('*', { count: 'exact', head: true })
        .eq('is_published', true),
      supabase
        .from("labubu_wallpapers")
        .select('*', { count: 'exact', head: true })
        .eq('is_published', true)
        .eq('wallpaper_type', 'live'),
      supabase
        .from("labubu_wallpapers")
        .select('*', { count: 'exact', head: true })
        .eq('is_published', true)
        .eq('wallpaper_type', 'static')
    ]);

    const stats = {
      total: totalCount ?? 0,
      live: liveCount ?? 0,
      static: staticCount ?? 0,
    };

    return respData(stats);

  } catch (error) {
    console.error("Failed to fetch wallpaper stats:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    return respErr(errorMessage);
  }
} 