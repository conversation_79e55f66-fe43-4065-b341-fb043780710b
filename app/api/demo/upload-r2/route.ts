import { respData, respErr } from "@/lib/resp";
import { newStorage } from "@/lib/storage";
import { promises as fs } from "fs";
import path from "path";

export async function GET() {
  try {
    const imagePath = path.join(process.cwd(), "public/r2imgs", "labubu1.png");
    const fileBuffer = await fs.readFile(imagePath);

    const storage = newStorage();

    const filename = `labubu-${Date.now()}.png`;
    const key = `test/${filename}`;

    const result = await storage.uploadFile({
      body: fileBuffer,
      key: key,
      contentType: "image/png",
    });

    return respData(result);
  } catch (error) {
    console.error("Upload to R2 failed:", error);
    if ((error as NodeJS.ErrnoException).code === "ENOENT") {
      return respErr(
        "Upload failed. Make sure 'labubu1.png' is in the 'public' directory."
      );
    }
    return respErr("upload to r2 failed");
  }
}
