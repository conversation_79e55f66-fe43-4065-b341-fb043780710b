import { respData, respErr } from "@/lib/resp";
import { newStorage } from "@/lib/storage";
import { getSupabaseClient } from "@/models/db";
import { NextRequest } from "next/server";
import { promises as fs } from "fs";
import path from "path";
import { processStaticImage } from "@/lib/admin/process-static";
import { processLiveWallpaper } from "@/lib/admin/process-live";

const getFileExtension = (filename: string) => path.extname(filename).slice(1).toLowerCase();

const getContentType = (extension: string): string => {
  const contentTypes: Record<string, string> = {
    jpg: "image/jpeg",
    jpeg: "image/jpeg",
    png: "image/png",
    webp: "image/webp",
    gif: "image/gif",
    mp4: "video/mp4",
  };
  return contentTypes[extension] || "application/octet-stream";
};

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();
    const { filename, title, description, device_target, is_published } = body;

    if (!filename || !title) {
      return respErr("Missing filename or title");
    }

    const filePath = path.join(process.cwd(), "public/admin-upload", filename);
    await fs.access(filePath);
    const fileBuffer = await fs.readFile(filePath);
    const fileExtension = getFileExtension(filename);

    let processedData;
    let wallpaperType: 'static' | 'live';

    if (['jpg', 'jpeg', 'png', 'webp', 'gif', 'heic', 'tiff'].includes(fileExtension)) {
      processedData = await processStaticImage(fileBuffer, filename);
      wallpaperType = 'static';
    } else if (fileExtension === 'mp4') {
      processedData = await processLiveWallpaper(fileBuffer, filename);
      wallpaperType = 'live';
    } else {
      return respErr(`Unsupported file type: ${fileExtension}`);
    }
    
    const { r2Keys, buffers, dbData: processedDbData } = processedData;

    // Upload all files to R2 in parallel
    const storage = newStorage();
    const uploadPromises = Object.entries(buffers).map(([key, buffer]) => {
        const r2Key = r2Keys[key as keyof typeof r2Keys];
        if (!r2Key) return Promise.resolve(null); // Should not happen

        let contentType;
        if (key === 'original') {
            contentType = getContentType(fileExtension);
        } else if (key === 'videoThumbnail') {
            contentType = 'video/mp4';
        } else {
            // All other previews/thumbnails are webp
            contentType = 'image/webp';
        }

        return storage.uploadFile({
            body: buffer,
            key: r2Key,
            contentType: contentType,
            // Apply disposition only to the main downloadable file
            disposition: key === 'original' ? 'attachment' : undefined,
        });
    });

    const uploadResults = await Promise.all(uploadPromises);

    // Write metadata to Supabase
    const finalDbData = {
      title,
      description: description || null,
      wallpaper_type: wallpaperType,
      device_target: device_target || "any",
      is_published: is_published !== false,
      ...processedDbData,
    };

    const supabase = getSupabaseClient();
    const { data: wallpaper, error } = await supabase
      .from("labubu_wallpapers")
      .insert(finalDbData)
      .select()
      .single();

    if (error) {
      console.error("Supabase insert error:", error);
      return respErr("Failed to save wallpaper metadata to database");
    }

    return respData({
      wallpaper,
      uploads: uploadResults,
      source_file: filename,
    });

  } catch (error) {
    console.error("Upload wallpaper failed:", error);
    const errorMessage = error instanceof Error ? error.message : "An unknown error occurred";
    // Check for specific file not found error from fs.access
    if (errorMessage.includes('no such file or directory')) {
      return respErr(`File not found in public/admin-upload/`);
    }
    return respErr(errorMessage);
  }
}
